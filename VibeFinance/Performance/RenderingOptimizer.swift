//
//  RenderingOptimizer.swift
//  VibeFinance - Advanced Rendering Performance Optimizer
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import SwiftUI
import MetalKit
import QuartzCore

// MARK: - Rendering Optimizer

class RenderingOptimizer: ObservableObject {
    @Published var targetFrameRate: Double = 60.0
    @Published var currentFrameRate: Double = 60.0
    @Published var renderingQuality: RenderingQuality = .high
    @Published var isAdaptiveRenderingEnabled = true
    @Published var isMetalAccelerationEnabled = true
    
    // Metal resources
    private var metalDevice: MTLDevice?
    private var metalCommandQueue: MTLCommandQueue?
    private var metalLibrary: MTLLibrary?
    
    // Frame rate monitoring
    private var frameRateMonitor: CADisplayLink?
    private var frameCount: Int = 0
    private var lastFrameTime: CFTimeInterval = 0
    
    // Rendering optimizations
    private var isLowMemoryMode = false
    private var isLowPowerMode = false
    private var currentOptimizationLevel: OptimizationLevel = .standard
    
    init() {
        setupMetalAcceleration()
        setupFrameRateMonitoring()
        configureRenderingOptimizations()
    }
    
    deinit {
        frameRateMonitor?.invalidate()
    }
    
    // MARK: - Setup
    
    private func setupMetalAcceleration() {
        guard let device = MTLCreateSystemDefaultDevice() else {
            ProductionConfig.log("⚠️ Metal not available, falling back to Core Graphics", category: "RENDERING", level: .warning)
            isMetalAccelerationEnabled = false
            return
        }
        
        metalDevice = device
        metalCommandQueue = device.makeCommandQueue()
        
        do {
            metalLibrary = try device.makeDefaultLibrary(bundle: Bundle.main)
            ProductionConfig.log("✅ Metal acceleration initialized", category: "RENDERING", level: .info)
        } catch {
            ProductionConfig.log("❌ Failed to create Metal library: \(error)", category: "RENDERING", level: .error)
            isMetalAccelerationEnabled = false
        }
    }
    
    private func setupFrameRateMonitoring() {
        frameRateMonitor = CADisplayLink(target: self, selector: #selector(updateFrameRate))
        frameRateMonitor?.add(to: .main, forMode: .common)
        frameRateMonitor?.preferredFramesPerSecond = Int(targetFrameRate)
    }
    
    @objc private func updateFrameRate() {
        let currentTime = CACurrentMediaTime()
        
        if lastFrameTime > 0 {
            let deltaTime = currentTime - lastFrameTime
            let instantFrameRate = 1.0 / deltaTime
            
            // Smooth the frame rate calculation
            currentFrameRate = (currentFrameRate * 0.9) + (instantFrameRate * 0.1)
        }
        
        lastFrameTime = currentTime
        frameCount += 1
        
        // Adaptive frame rate adjustment
        if isAdaptiveRenderingEnabled {
            adaptFrameRate()
        }
    }
    
    private func configureRenderingOptimizations() {
        // Configure based on device capabilities
        let deviceModel = UIDevice.current.model
        let systemVersion = UIDevice.current.systemVersion
        
        if deviceModel.contains("iPad Pro") || deviceModel.contains("iPhone 15 Pro") {
            // High-end devices can handle maximum quality
            renderingQuality = .ultra
            targetFrameRate = 120.0
        } else if deviceModel.contains("iPhone 13") || deviceModel.contains("iPhone 14") {
            // Mid-range devices use high quality
            renderingQuality = .high
            targetFrameRate = 60.0
        } else {
            // Older devices use balanced settings
            renderingQuality = .medium
            targetFrameRate = 60.0
        }
        
        ProductionConfig.log("🎨 Rendering configured for \(deviceModel) with \(renderingQuality.displayName) quality", category: "RENDERING", level: .info)
    }
    
    // MARK: - Frame Rate Management
    
    func setTargetFrameRate(_ frameRate: Double) {
        targetFrameRate = frameRate
        frameRateMonitor?.preferredFramesPerSecond = Int(frameRate)
        
        ProductionConfig.log("🎯 Target frame rate set to \(frameRate) fps", category: "RENDERING", level: .info)
    }
    
    private func adaptFrameRate() {
        let frameRateDifference = abs(currentFrameRate - targetFrameRate)
        
        // If we're significantly below target, reduce quality
        if currentFrameRate < targetFrameRate * 0.8 {
            reduceRenderingQuality()
        }
        // If we're consistently above target, we can increase quality
        else if currentFrameRate > targetFrameRate * 1.1 && frameRateDifference < 5 {
            increaseRenderingQuality()
        }
    }
    
    // MARK: - Quality Management
    
    private func reduceRenderingQuality() {
        switch renderingQuality {
        case .ultra:
            renderingQuality = .high
        case .high:
            renderingQuality = .medium
        case .medium:
            renderingQuality = .low
        case .low:
            break // Already at minimum
        }
        
        applyQualitySettings()
    }
    
    private func increaseRenderingQuality() {
        guard !isLowMemoryMode && !isLowPowerMode else { return }
        
        switch renderingQuality {
        case .low:
            renderingQuality = .medium
        case .medium:
            renderingQuality = .high
        case .high:
            renderingQuality = .ultra
        case .ultra:
            break // Already at maximum
        }
        
        applyQualitySettings()
    }
    
    private func applyQualitySettings() {
        switch renderingQuality {
        case .ultra:
            configureUltraQuality()
        case .high:
            configureHighQuality()
        case .medium:
            configureMediumQuality()
        case .low:
            configureLowQuality()
        }
        
        ProductionConfig.log("🎨 Rendering quality adjusted to \(renderingQuality.displayName)", category: "RENDERING", level: .info)
    }
    
    private func configureUltraQuality() {
        // Maximum quality settings
        setAnimationDuration(1.0)
        setBlurRadius(20.0)
        setShadowRadius(10.0)
        setGradientStops(10)
    }
    
    private func configureHighQuality() {
        // High quality settings
        setAnimationDuration(0.8)
        setBlurRadius(15.0)
        setShadowRadius(8.0)
        setGradientStops(8)
    }
    
    private func configureMediumQuality() {
        // Balanced settings
        setAnimationDuration(0.6)
        setBlurRadius(10.0)
        setShadowRadius(5.0)
        setGradientStops(6)
    }
    
    private func configureLowQuality() {
        // Performance-focused settings
        setAnimationDuration(0.3)
        setBlurRadius(5.0)
        setShadowRadius(2.0)
        setGradientStops(4)
    }
    
    // MARK: - Optimization Modes
    
    func enableLowMemoryMode() {
        isLowMemoryMode = true
        renderingQuality = .low
        targetFrameRate = 30.0
        
        // Disable expensive effects
        disableExpensiveEffects()
        
        ProductionConfig.log("🧠 Low memory rendering mode enabled", category: "RENDERING", level: .warning)
    }
    
    func enableLowPowerMode() {
        isLowPowerMode = true
        renderingQuality = .medium
        targetFrameRate = 30.0
        
        // Reduce animation frequency
        reduceAnimationFrequency()
        
        ProductionConfig.log("🔋 Low power rendering mode enabled", category: "RENDERING", level: .info)
    }
    
    func enablePerformanceMode() {
        isLowMemoryMode = false
        isLowPowerMode = false
        renderingQuality = .ultra
        targetFrameRate = 120.0
        
        // Enable all effects
        enableAllEffects()
        
        ProductionConfig.log("⚡ Performance rendering mode enabled", category: "RENDERING", level: .info)
    }
    
    func enableBalancedMode() {
        isLowMemoryMode = false
        isLowPowerMode = false
        renderingQuality = .high
        targetFrameRate = 60.0
        
        applyQualitySettings()
        
        ProductionConfig.log("⚖️ Balanced rendering mode enabled", category: "RENDERING", level: .info)
    }
    
    func enableAdaptiveMode() {
        isAdaptiveRenderingEnabled = true
        
        ProductionConfig.log("🤖 Adaptive rendering mode enabled", category: "RENDERING", level: .info)
    }
    
    // MARK: - Optimization Application
    
    func applyOptimization(level: OptimizationLevel) {
        currentOptimizationLevel = level
        
        switch level {
        case .minimal:
            enablePerformanceMode()
        case .standard:
            enableBalancedMode()
        case .aggressive:
            enableLowMemoryMode()
        case .adaptive:
            enableAdaptiveMode()
        }
    }
    
    // MARK: - Effect Management
    
    private func disableExpensiveEffects() {
        // Disable blur effects
        setBlurRadius(0.0)
        
        // Disable shadows
        setShadowRadius(0.0)
        
        // Simplify gradients
        setGradientStops(2)
        
        // Disable animations
        setAnimationDuration(0.0)
    }
    
    private func enableAllEffects() {
        configureUltraQuality()
    }
    
    private func reduceAnimationFrequency() {
        // Reduce animation update frequency
        setAnimationDuration(0.5)
    }
    
    // MARK: - Rendering Settings
    
    private func setAnimationDuration(_ duration: Double) {
        // This would be used by SwiftUI animations
        UserDefaults.standard.set(duration, forKey: "AnimationDuration")
    }
    
    private func setBlurRadius(_ radius: Double) {
        UserDefaults.standard.set(radius, forKey: "BlurRadius")
    }
    
    private func setShadowRadius(_ radius: Double) {
        UserDefaults.standard.set(radius, forKey: "ShadowRadius")
    }
    
    private func setGradientStops(_ stops: Int) {
        UserDefaults.standard.set(stops, forKey: "GradientStops")
    }
    
    // MARK: - Metal Acceleration
    
    func optimizeChartRendering() {
        guard isMetalAccelerationEnabled,
              let device = metalDevice,
              let commandQueue = metalCommandQueue else {
            return
        }
        
        // Configure Metal for chart rendering optimization
        ProductionConfig.log("📊 Metal chart rendering optimization enabled", category: "RENDERING", level: .info)
    }
    
    func optimizeImageProcessing() {
        guard isMetalAccelerationEnabled else { return }
        
        // Use Metal Performance Shaders for image processing
        ProductionConfig.log("🖼️ Metal image processing optimization enabled", category: "RENDERING", level: .info)
    }
    
    // MARK: - Public Interface
    
    func getCurrentFrameRate() -> Double {
        return currentFrameRate
    }
    
    func getRenderingQuality() -> RenderingQuality {
        return renderingQuality
    }
    
    func setRenderingQuality(_ quality: RenderingQuality) {
        renderingQuality = quality
        applyQualitySettings()
    }
    
    func getPerformanceMetrics() -> RenderingPerformanceMetrics {
        return RenderingPerformanceMetrics(
            currentFrameRate: currentFrameRate,
            targetFrameRate: targetFrameRate,
            renderingQuality: renderingQuality,
            isMetalEnabled: isMetalAccelerationEnabled,
            optimizationLevel: currentOptimizationLevel
        )
    }
}

// MARK: - Supporting Types

enum RenderingQuality: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case ultra = "ultra"
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        case .ultra: return "Ultra"
        }
    }
    
    var qualityScore: Double {
        switch self {
        case .low: return 0.25
        case .medium: return 0.5
        case .high: return 0.75
        case .ultra: return 1.0
        }
    }
}

struct RenderingPerformanceMetrics {
    let currentFrameRate: Double
    let targetFrameRate: Double
    let renderingQuality: RenderingQuality
    let isMetalEnabled: Bool
    let optimizationLevel: OptimizationLevel
    
    var frameRateEfficiency: Double {
        return min(1.0, currentFrameRate / targetFrameRate)
    }
    
    var overallScore: Double {
        return (frameRateEfficiency + renderingQuality.qualityScore) / 2.0
    }
}

// MARK: - Performance Monitor Delegate

extension RenderingOptimizer: PerformanceMonitorDelegate {
    func performanceMonitor(_ monitor: PerformanceMonitor, didUpdateMetrics metrics: SystemMetrics) {
        // Adjust rendering based on system performance
        if metrics.memoryUsage > 0.8 {
            enableLowMemoryMode()
        } else if metrics.cpuUsage > 0.8 {
            reduceRenderingQuality()
        } else if metrics.batteryLevel < 0.2 {
            enableLowPowerMode()
        }
    }
}

// MARK: - Performance Monitor Protocol

protocol PerformanceMonitorDelegate: AnyObject {
    func performanceMonitor(_ monitor: PerformanceMonitor, didUpdateMetrics metrics: SystemMetrics)
}

class PerformanceMonitor {
    weak var delegate: PerformanceMonitorDelegate?
    private var isMonitoring = false
    
    func startMonitoring() {
        isMonitoring = true
        // Implementation would monitor system metrics
    }
    
    func stopMonitoring() {
        isMonitoring = false
    }
}
