//
//  SimulatorManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class SimulatorManager: ObservableObject {
    @Published var virtualBalance: Double = 10000.0 // Starting with $10K
    @Published var holdings: [SimulatorHolding] = []
    @Published var transactions: [SimulatorTransaction] = []
    @Published var watchlists: [Watchlist] = []
    @Published var marketData: [String: StockPrice] = [:]
    @Published var portfolio: VirtualPortfolio?
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let supabaseService = SupabaseService.shared
    private let stockService = StockServiceManager.shared
    private let marketService = MarketService.shared
    private var cancellables = Set<AnyCancellable>()
    private var priceUpdateTimer: Timer?

    // MARK: - Computed Properties

    var totalPortfolioValue: Double {
        virtualBalance + holdings.reduce(0) { $0 + $1.currentValue }
    }

    var dailyChange: Double {
        holdings.reduce(0) { $0 + $1.gainLoss }
    }

    var dailyChangePercent: Double {
        let totalInvested = holdings.reduce(0) { $0 + $1.costBasis }
        guard totalInvested > 0 else { return 0 }
        return (dailyChange / totalInvested) * 100
    }

    var totalReturn: Double {
        let initialValue = 10000.0
        return ((totalPortfolioValue - initialValue) / initialValue) * 100
    }
    
    // MARK: - Portfolio Management

    func loadPortfolio() async {
        isLoading = true
        errorMessage = nil

        do {
            // Load user's simulator data from database
            let userID = getCurrentUserID()

            // Load holdings
            let holdingsData: [SimulatorHolding] = try await supabaseService.fetch(
                SimulatorHolding.self,
                from: "simulator_holdings",
                where: "user_id=eq.\(userID.uuidString)"
            )

            // Load transactions
            let transactionsData: [SimulatorTransaction] = try await supabaseService.fetch(
                SimulatorTransaction.self,
                from: "simulator_transactions",
                where: "user_id=eq.\(userID.uuidString)"
            )

            // Update current prices for holdings
            var updatedHoldings: [SimulatorHolding] = []
            for holding in holdingsData {
                if let currentPrice = await stockService.getStockData(symbol: holding.symbol) {
                    let updatedHolding = SimulatorHolding(
                        id: holding.id,
                        userID: holding.userID,
                        symbol: holding.symbol,
                        shares: holding.shares,
                        averagePrice: holding.averagePrice,
                        currentPrice: currentPrice.price,
                        purchaseDate: holding.purchaseDate
                    )
                    updatedHoldings.append(updatedHolding)
                } else {
                    updatedHoldings.append(holding)
                }
            }

            await MainActor.run {
                self.holdings = updatedHoldings
                self.transactions = transactionsData
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func loadWatchlists(for userID: UUID) async {
        do {
            let lists = try await supabaseService.fetch(
                Watchlist.self,
                from: "watchlists",
                where: "userID=eq.\(userID.uuidString)"
            )
            
            await MainActor.run {
                self.watchlists = lists
            }
            
        } catch {
            print("Failed to load watchlists: \(error)")
        }
    }
    
    // MARK: - Trading Operations
    
    func buyStock(symbol: String, shares: Double) async {
        guard var currentPortfolio = portfolio else { return }
        
        do {
            let quote = try await marketService.getQuote(symbol: symbol)
            let totalCost = shares * quote.price
            
            guard currentPortfolio.balance >= totalCost else {
                errorMessage = "Insufficient funds"
                return
            }
            
            let position = VirtualPosition(
                symbol: symbol,
                name: "\(quote.symbol) Inc.",
                shares: shares,
                averageCost: quote.price,
                currentPrice: quote.price
            )
            
            currentPortfolio.addPosition(position)
            
            let transaction = VirtualTransaction(
                portfolioID: currentPortfolio.id,
                type: .buy,
                symbol: symbol,
                name: "\(quote.symbol) Inc.",
                shares: shares,
                price: quote.price
            )
            
            currentPortfolio.transactions.append(transaction)
            
            try await supabaseService.update(
                currentPortfolio,
                in: "virtual_portfolios",
                where: "id=eq.\(currentPortfolio.id.uuidString)"
            )
            
            await MainActor.run {
                self.portfolio = currentPortfolio
                self.marketData[symbol] = quote
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func sellStock(symbol: String, shares: Double) async {
        guard var currentPortfolio = portfolio else { return }
        guard let position = currentPortfolio.positions.first(where: { $0.symbol == symbol }),
              position.shares >= shares else {
            errorMessage = "Insufficient shares"
            return
        }
        
        do {
            let quote = try await marketService.getQuote(symbol: symbol)
            
            currentPortfolio.removePosition(symbol, shares: shares)
            
            let transaction = VirtualTransaction(
                portfolioID: currentPortfolio.id,
                type: .sell,
                symbol: symbol,
                name: "\(quote.symbol) Inc.",
                shares: shares,
                price: quote.price
            )
            
            currentPortfolio.transactions.append(transaction)
            
            try await supabaseService.update(
                currentPortfolio,
                in: "virtual_portfolios",
                where: "id=eq.\(currentPortfolio.id.uuidString)"
            )
            
            await MainActor.run {
                self.portfolio = currentPortfolio
                self.marketData[symbol] = quote
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    // MARK: - Market Data
    
    func searchStocks(query: String) async -> [StockPrice] {
        do {
            return try await marketService.searchStocks(query: query)
        } catch {
            print("Failed to search stocks: \(error)")
            return []
        }
    }
    
    func getQuote(symbol: String) async -> StockPrice? {
        do {
            let quote = try await marketService.getQuote(symbol: symbol)
            await MainActor.run {
                self.marketData[symbol] = quote
            }
            return quote
        } catch {
            print("Failed to get quote for \(symbol): \(error)")
            return nil
        }
    }
    
    func updatePortfolioPrices() async {
        guard var currentPortfolio = portfolio else { return }
        
        var updatedMarketData: [StockPrice] = []
        
        for position in currentPortfolio.positions {
            if let quote = await getQuote(symbol: position.symbol) {
                updatedMarketData.append(quote)
            }
        }
        
        currentPortfolio.updatePrices(updatedMarketData)
        
        do {
            try await supabaseService.update(
                currentPortfolio,
                in: "virtual_portfolios",
                where: "id=eq.\(currentPortfolio.id.uuidString)"
            )
            
            await MainActor.run {
                self.portfolio = currentPortfolio
            }
            
        } catch {
            print("Failed to update portfolio prices: \(error)")
        }
    }
    
    func startPriceUpdates() {
        priceUpdateTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            Task {
                await self.updatePortfolioPrices()
            }
        }
    }
    
    func stopPriceUpdates() {
        priceUpdateTimer?.invalidate()
        priceUpdateTimer = nil
    }
    
    // MARK: - Watchlist Management
    
    func createWatchlist(name: String, userID: UUID) async {
        let watchlist = Watchlist(userID: userID, name: name)
        
        do {
            try await supabaseService.create(watchlist, table: "watchlists")
            
            await MainActor.run {
                self.watchlists.append(watchlist)
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func addToWatchlist(_ watchlistID: UUID, symbol: String) async {
        guard let index = watchlists.firstIndex(where: { $0.id == watchlistID }) else { return }
        
        var watchlist = watchlists[index]
        if !watchlist.symbols.contains(symbol) {
            watchlist.symbols.append(symbol)
            watchlist.updatedAt = Date()
            
            do {
                try await supabaseService.update(
                    watchlist,
                    in: "watchlists",
                    where: "id=eq.\(watchlistID.uuidString)"
                )
                
                await MainActor.run {
                    self.watchlists[index] = watchlist
                }
                
            } catch {
                await MainActor.run {
                    self.errorMessage = error.localizedDescription
                }
            }
        }
    }
    
    func removeFromWatchlist(_ watchlistID: UUID, symbol: String) async {
        guard let index = watchlists.firstIndex(where: { $0.id == watchlistID }) else { return }
        
        var watchlist = watchlists[index]
        watchlist.symbols.removeAll { $0 == symbol }
        watchlist.updatedAt = Date()
        
        do {
            try await supabaseService.update(
                watchlist,
                in: "watchlists",
                where: "id=eq.\(watchlistID.uuidString)"
            )
            
            await MainActor.run {
                self.watchlists[index] = watchlist
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    // MARK: - Portfolio Analytics
    
    func getPortfolioPerformance() -> PortfolioPerformance? {
        guard let portfolio = portfolio else { return nil }
        
        let totalReturn = portfolio.totalReturn
        let totalReturnPercent = portfolio.totalReturnPercent
        
        // Calculate day change (simplified)
        let dayChange = portfolio.positions.reduce(0) { $0 + $1.dayChange * $1.shares }
        let dayChangePercent = portfolio.totalInvested > 0 ? (dayChange / portfolio.totalInvested) * 100 : 0
        
        let bestPerformer = portfolio.positions.max { $0.totalReturnPercent < $1.totalReturnPercent }?.symbol
        let worstPerformer = portfolio.positions.min { $0.totalReturnPercent < $1.totalReturnPercent }?.symbol
        
        return PortfolioPerformance(
            totalReturn: totalReturn,
            dailyChange: dayChange,
            weeklyChange: dayChange * 7,
            monthlyChange: dayChange * 30,
            yearlyChange: totalReturn
        )
    }
    
    // MARK: - Helper Methods
    
    func clearError() {
        errorMessage = nil
    }
    
    func resetPortfolio() async {
        do {
            let userID = getCurrentUserID()

            // Clear all holdings
            try await supabaseService.delete(
                from: "simulator_holdings",
                where: "user_id=eq.\(userID.uuidString)"
            )

            // Clear all transactions
            try await supabaseService.delete(
                from: "simulator_transactions",
                where: "user_id=eq.\(userID.uuidString)"
            )

            // Reset virtual balance
            try await supabaseService.update(
                ["virtual_balance": 10000.0],
                table: "simulator_users",
                where: "user_id=eq.\(userID.uuidString)"
            )

            await MainActor.run {
                self.virtualBalance = 10000.0
                self.holdings.removeAll()
                self.transactions.removeAll()
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to reset portfolio: \(error.localizedDescription)"
            }
        }
    }

    // MARK: - Helper Methods

    private func getCurrentUserID() -> UUID {
        // Get from auth manager or user manager
        return UUID() // Placeholder
    }
}

// MARK: - Supporting Models

struct SimulatorHolding: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    let symbol: String
    let shares: Double
    let averagePrice: Double
    let currentPrice: Double
    let purchaseDate: Date

    init(id: UUID = UUID(), userID: UUID, symbol: String, shares: Double, averagePrice: Double, currentPrice: Double, purchaseDate: Date) {
        self.id = id
        self.userID = userID
        self.symbol = symbol
        self.shares = shares
        self.averagePrice = averagePrice
        self.currentPrice = currentPrice
        self.purchaseDate = purchaseDate
    }

    var currentValue: Double {
        shares * currentPrice
    }

    var costBasis: Double {
        shares * averagePrice
    }

    var gainLoss: Double {
        currentValue - costBasis
    }

    var returnPercent: Double {
        guard costBasis > 0 else { return 0 }
        return (gainLoss / costBasis) * 100
    }
}

struct SimulatorTransaction: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    let symbol: String
    let type: TransactionType
    let shares: Double
    let price: Double
    let totalAmount: Double
    let timestamp: Date

    init(id: UUID = UUID(), userID: UUID, symbol: String, type: TransactionType, shares: Double, price: Double, totalAmount: Double, timestamp: Date) {
        self.id = id
        self.userID = userID
        self.symbol = symbol
        self.type = type
        self.shares = shares
        self.price = price
        self.totalAmount = totalAmount
        self.timestamp = timestamp
    }
}


