import SwiftUI
import Combine

// MARK: - Theme Manager
@MainActor
class ThemeManager: ObservableObject {
    @Published var currentTheme: AppTheme = .dark
    @Published var useSystemTheme: Bool = false
    
    private let userDefaults = UserDefaults.standard
    private let themeKey = "selectedTheme"
    private let systemThemeKey = "useSystemTheme"
    
    init() {
        loadThemePreference()
    }
    
    func setTheme(_ theme: AppTheme) {
        currentTheme = theme
        useSystemTheme = false
        saveThemePreference()
    }
    
    func toggleSystemTheme() {
        useSystemTheme.toggle()
        if useSystemTheme {
            updateThemeFromSystem()
        }
        saveThemePreference()
    }
    
    func updateThemeFromSystem() {
        guard useSystemTheme else { return }
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            currentTheme = window.traitCollection.userInterfaceStyle == .dark ? .dark : .light
        }
    }
    
    private func loadThemePreference() {
        useSystemTheme = userDefaults.bool(forKey: systemThemeKey)
        
        if useSystemTheme {
            updateThemeFromSystem()
        } else {
            let themeRawValue = userDefaults.string(forKey: themeKey) ?? AppTheme.dark.rawValue
            currentTheme = AppTheme(rawValue: themeRawValue) ?? .dark
        }
    }
    
    private func saveThemePreference() {
        userDefaults.set(currentTheme.rawValue, forKey: themeKey)
        userDefaults.set(useSystemTheme, forKey: systemThemeKey)
    }
}

// MARK: - App Theme Enum
enum AppTheme: String, CaseIterable {
    case light = "light"
    case dark = "dark"
    
    var displayName: String {
        switch self {
        case .light: return "Light"
        case .dark: return "Dark"
        }
    }
    
    var colorScheme: ColorScheme {
        switch self {
        case .light: return .light
        case .dark: return .dark
        }
    }
}

// MARK: - Theme Colors
struct ThemeColors {
    let primary: Color
    let secondary: Color
    let accent: Color
    let background: Color
    let surface: Color
    let onPrimary: Color
    let onSecondary: Color
    let onBackground: Color
    let onSurface: Color
    let success: Color
    let warning: Color
    let error: Color
    
    static let light = ThemeColors(
        primary: Color(red: 0.1, green: 0.1, blue: 0.2),
        secondary: Color(red: 0.2, green: 0.2, blue: 0.3),
        accent: Color(red: 1.0, green: 0.84, blue: 0.0), // Warren Buffett Gold
        background: Color(red: 0.98, green: 0.98, blue: 0.99),
        surface: Color.white,
        onPrimary: Color.white,
        onSecondary: Color.white,
        onBackground: Color(red: 0.1, green: 0.1, blue: 0.1),
        onSurface: Color(red: 0.1, green: 0.1, blue: 0.1),
        success: Color.green,
        warning: Color.orange,
        error: Color.red
    )
    
    static let dark = ThemeColors(
        primary: Color(red: 0.1, green: 0.1, blue: 0.2),
        secondary: Color(red: 0.2, green: 0.2, blue: 0.3),
        accent: Color(red: 1.0, green: 0.84, blue: 0.0), // Warren Buffett Gold
        background: Color.black,
        surface: Color(red: 0.1, green: 0.1, blue: 0.15),
        onPrimary: Color.white,
        onSecondary: Color.white,
        onBackground: Color.white,
        onSurface: Color.white,
        success: Color.green,
        warning: Color.orange,
        error: Color.red
    )
}

// MARK: - Theme Environment Key
struct ThemeEnvironmentKey: EnvironmentKey {
    static let defaultValue: ThemeColors = ThemeColors.dark
}

extension EnvironmentValues {
    var theme: ThemeColors {
        get { self[ThemeEnvironmentKey.self] }
        set { self[ThemeEnvironmentKey.self] = newValue }
    }
}

// MARK: - Theme Modifier
struct ThemedView: ViewModifier {
    @ObservedObject var themeManager: ThemeManager
    
    func body(content: Content) -> some View {
        content
            .environment(\.theme, themeManager.currentTheme == .dark ? ThemeColors.dark : ThemeColors.light)
            .preferredColorScheme(themeManager.currentTheme.colorScheme)
    }
}

extension View {
    func themed(_ themeManager: ThemeManager) -> some View {
        modifier(ThemedView(themeManager: themeManager))
    }
}
