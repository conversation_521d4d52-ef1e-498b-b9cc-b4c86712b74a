//
//  TradingSafetyModels.swift
//  VibeFinance - Trading Safety Data Models
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import SwiftUI

// MARK: - Core Trading Models

struct TradingSafetyOrder: Identifiable, Codable {
    let id: UUID
    let symbol: String
    let side: OrderSide
    let quantity: Double
    let orderType: OrderType
    let limitPrice: Double?
    let stopPrice: Double?
    let timeInForce: TimeInForce
    let totalValue: Double
    let estimatedFees: Double
    let riskLevel: RiskLevel
    let timestamp: Date
    
    init(id: UUID = UUID(), symbol: String, side: OrderSide, quantity: Double, orderType: OrderType, limitPrice: Double? = nil, stopPrice: Double? = nil, timeInForce: TimeInForce = .gtc, totalValue: Double, estimatedFees: Double = 0, riskLevel: RiskLevel = .medium, timestamp: Date = Date()) {
        self.id = id
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        self.orderType = orderType
        self.limitPrice = limitPrice
        self.stopPrice = stopPrice
        self.timeInForce = timeInForce
        self.totalValue = totalValue
        self.estimatedFees = estimatedFees
        self.riskLevel = riskLevel
        self.timestamp = timestamp
    }
}

enum OrderSide: String, CaseIterable, Codable {
    case buy = "buy"
    case sell = "sell"
    
    var displayName: String {
        switch self {
        case .buy: return "Buy"
        case .sell: return "Sell"
        }
    }
    
    var color: Color {
        switch self {
        case .buy: return .green
        case .sell: return .red
        }
    }
}

enum OrderType: String, CaseIterable, Codable {
    case market = "market"
    case limit = "limit"
    case stop = "stop"
    case stopLimit = "stop_limit"
    
    var displayName: String {
        switch self {
        case .market: return "Market"
        case .limit: return "Limit"
        case .stop: return "Stop"
        case .stopLimit: return "Stop Limit"
        }
    }
    
    var description: String {
        switch self {
        case .market: return "Execute immediately at current market price"
        case .limit: return "Execute only at specified price or better"
        case .stop: return "Execute when price reaches stop level"
        case .stopLimit: return "Execute as limit order when stop price is reached"
        }
    }
}

enum TimeInForce: String, CaseIterable, Codable {
    case gtc = "gtc" // Good Till Canceled
    case day = "day" // Day Order
    case ioc = "ioc" // Immediate or Cancel
    case fok = "fok" // Fill or Kill
    
    var displayName: String {
        switch self {
        case .gtc: return "Good Till Canceled"
        case .day: return "Day Order"
        case .ioc: return "Immediate or Cancel"
        case .fok: return "Fill or Kill"
        }
    }
}

enum RiskLevel: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case extreme = "extreme"
    
    var displayName: String {
        switch self {
        case .low: return "Low Risk"
        case .medium: return "Medium Risk"
        case .high: return "High Risk"
        case .extreme: return "Extreme Risk"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .extreme: return .red
        }
    }
    
    var iconName: String {
        switch self {
        case .low: return "checkmark.shield"
        case .medium: return "exclamationmark.shield"
        case .high: return "exclamationmark.triangle"
        case .extreme: return "xmark.shield"
        }
    }
}

// MARK: - Validation Models

struct TradeValidationResult {
    let isValid: Bool
    let issues: [ValidationIssue]
    let warnings: [TradeWarning]
    let requiresConfirmation: Bool
    let riskLevel: RiskLevel
}

enum ValidationIssue: Equatable {
    case invalidQuantity(String)
    case invalidSymbol(String)
    case insufficientFunds(String)
    case orderTooLarge(String)
    case dailyLimitExceeded(LimitType)
    case pdtViolation(String)
    case marketClosed(String)
    case symbolRestricted(String)
    
    var title: String {
        switch self {
        case .invalidQuantity: return "Invalid Quantity"
        case .invalidSymbol: return "Invalid Symbol"
        case .insufficientFunds: return "Insufficient Funds"
        case .orderTooLarge: return "Order Too Large"
        case .dailyLimitExceeded: return "Daily Limit Exceeded"
        case .pdtViolation: return "Pattern Day Trading Violation"
        case .marketClosed: return "Market Closed"
        case .symbolRestricted: return "Symbol Restricted"
        }
    }
    
    var description: String {
        switch self {
        case .invalidQuantity(let message),
             .invalidSymbol(let message),
             .insufficientFunds(let message),
             .orderTooLarge(let message),
             .pdtViolation(let message),
             .marketClosed(let message),
             .symbolRestricted(let message):
            return message
        case .dailyLimitExceeded(let limitType):
            return "You have exceeded your daily \(limitType.displayName.lowercased()) limit"
        }
    }
    
    var severity: ValidationSeverity {
        switch self {
        case .invalidQuantity, .invalidSymbol, .insufficientFunds, .dailyLimitExceeded, .pdtViolation:
            return .error
        case .orderTooLarge, .marketClosed, .symbolRestricted:
            return .warning
        }
    }
}

enum TradeWarning: Equatable {
    case highVolatility(Double)
    case concentrationRisk(Double)
    case afterHoursTrading(Date)
    case approachingDailyLimit(LimitType)
    case highRiskFactor(RiskFactorType, String)
    case largeOrderSize(Double)
    case unusualMarketConditions(String)
    
    var title: String {
        switch self {
        case .highVolatility: return "High Volatility"
        case .concentrationRisk: return "Concentration Risk"
        case .afterHoursTrading: return "After Hours Trading"
        case .approachingDailyLimit: return "Approaching Daily Limit"
        case .highRiskFactor: return "High Risk Factor"
        case .largeOrderSize: return "Large Order"
        case .unusualMarketConditions: return "Unusual Market Conditions"
        }
    }
    
    var description: String {
        switch self {
        case .highVolatility(let level):
            return "This stock has high volatility (\(String(format: "%.1f", level))%). Price may fluctuate significantly."
        case .concentrationRisk(let percentage):
            return "This trade will increase your position to \(String(format: "%.1f", percentage))% of your portfolio."
        case .afterHoursTrading(let nextOpen):
            return "Market is closed. Order will execute when market opens at \(nextOpen.formatted(date: .omitted, time: .shortened))."
        case .approachingDailyLimit(let limitType):
            return "You are approaching your daily \(limitType.displayName.lowercased()) limit."
        case .highRiskFactor(_, let description):
            return description
        case .largeOrderSize(let amount):
            return "This is a large order (\(String(format: "%.0f", amount))). Consider breaking it into smaller orders."
        case .unusualMarketConditions(let description):
            return description
        }
    }
    
    var iconName: String {
        switch self {
        case .highVolatility: return "waveform.path.ecg"
        case .concentrationRisk: return "chart.pie"
        case .afterHoursTrading: return "clock"
        case .approachingDailyLimit: return "gauge"
        case .highRiskFactor: return "exclamationmark.triangle"
        case .largeOrderSize: return "arrow.up.circle"
        case .unusualMarketConditions: return "exclamationmark.bubble"
        }
    }
}

enum ValidationSeverity {
    case error, warning, info
    
    var color: Color {
        switch self {
        case .error: return .red
        case .warning: return .orange
        case .info: return .blue
        }
    }
}

// MARK: - Safety Settings

struct TradingSafetySettings: Codable {
    let maxOrderSize: Double
    let largeOrderThreshold: Double
    let authenticationTimeout: TimeInterval
    let requireBiometricAuth: Bool
    let enableDailyLimits: Bool
    let enableRiskWarnings: Bool
    let enableConcentrationChecks: Bool
    let maxConcentrationPercentage: Double
    
    static let `default` = TradingSafetySettings(
        maxOrderSize: 100000,
        largeOrderThreshold: 10000,
        authenticationTimeout: 300, // 5 minutes
        requireBiometricAuth: true,
        enableDailyLimits: true,
        enableRiskWarnings: true,
        enableConcentrationChecks: true,
        maxConcentrationPercentage: 20.0
    )
}

struct DailyTradingLimits: Codable {
    let maxTradesPerDay: Int
    let maxVolumePerDay: Double
    let maxLossPerDay: Double
    
    static let `default` = DailyTradingLimits(
        maxTradesPerDay: 10,
        maxVolumePerDay: 50000,
        maxLossPerDay: 5000
    )
    
    static let conservative = DailyTradingLimits(
        maxTradesPerDay: 5,
        maxVolumePerDay: 25000,
        maxLossPerDay: 2500
    )
    
    static let aggressive = DailyTradingLimits(
        maxTradesPerDay: 25,
        maxVolumePerDay: 100000,
        maxLossPerDay: 10000
    )
}

struct RiskToleranceSettings: Codable {
    let level: RiskToleranceLevel
    let maxVolatilityThreshold: Double
    let maxBetaThreshold: Double
    let requireConfirmationForHighRisk: Bool
    
    static let conservative = RiskToleranceSettings(
        level: .conservative,
        maxVolatilityThreshold: 0.15,
        maxBetaThreshold: 0.8,
        requireConfirmationForHighRisk: true
    )
    
    static let moderate = RiskToleranceSettings(
        level: .moderate,
        maxVolatilityThreshold: 0.25,
        maxBetaThreshold: 1.2,
        requireConfirmationForHighRisk: true
    )
    
    static let aggressive = RiskToleranceSettings(
        level: .aggressive,
        maxVolatilityThreshold: 0.40,
        maxBetaThreshold: 1.8,
        requireConfirmationForHighRisk: false
    )
}

enum RiskToleranceLevel: String, CaseIterable, Codable {
    case conservative = "conservative"
    case moderate = "moderate"
    case aggressive = "aggressive"
    
    var displayName: String {
        switch self {
        case .conservative: return "Conservative"
        case .moderate: return "Moderate"
        case .aggressive: return "Aggressive"
        }
    }
}

// MARK: - Risk Assessment Models

struct RiskAssessment {
    let riskLevel: RiskLevel
    let riskFactors: [RiskFactor]
    let warnings: [TradeWarning]
    let overallScore: Double
}

struct RiskFactor {
    let type: RiskFactorType
    let score: Double // 0.0 to 1.0
    let description: String
    let impact: RiskImpact
}

enum RiskFactorType: String, CaseIterable {
    case orderSize = "order_size"
    case volatility = "volatility"
    case concentration = "concentration"
    case marketTiming = "market_timing"
    case liquidity = "liquidity"
    case beta = "beta"
    
    var displayName: String {
        switch self {
        case .orderSize: return "Order Size"
        case .volatility: return "Volatility"
        case .concentration: return "Concentration"
        case .marketTiming: return "Market Timing"
        case .liquidity: return "Liquidity"
        case .beta: return "Market Sensitivity"
        }
    }
}

enum RiskImpact: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }
}

// MARK: - Authentication Models

enum AuthenticationResult {
    case success
    case failed(String)
    case fallbackToPasscode
    case cancelled
}

// MARK: - Confirmation Flow Models

struct TradeConfirmationFlow {
    let orderId: UUID
    let steps: [ConfirmationStep]
    var currentStepIndex: Int
    var isCompleted: Bool
    var userResponses: [String: Any] = [:]
}

enum ConfirmationStep {
    case orderReview(TradeOrder)
    case riskWarnings([TradeWarning])
    case costBreakdown(TradeCostBreakdown)
    case impactAnalysis(PortfolioImpactAnalysis)
    case authentication
    case finalConfirmation
    
    var title: String {
        switch self {
        case .orderReview: return "Review Order"
        case .riskWarnings: return "Risk Warnings"
        case .costBreakdown: return "Cost Breakdown"
        case .impactAnalysis: return "Portfolio Impact"
        case .authentication: return "Authentication"
        case .finalConfirmation: return "Final Confirmation"
        }
    }
    
    var iconName: String {
        switch self {
        case .orderReview: return "doc.text.magnifyingglass"
        case .riskWarnings: return "exclamationmark.triangle"
        case .costBreakdown: return "dollarsign.circle"
        case .impactAnalysis: return "chart.pie"
        case .authentication: return "faceid"
        case .finalConfirmation: return "checkmark.circle"
        }
    }
}

// MARK: - Supporting Models

struct TradeCostBreakdown {
    let orderValue: Double
    let estimatedFees: Double
    let marketImpact: Double
    let totalCost: Double
    let breakdown: [CostComponent]
}

struct CostComponent {
    let name: String
    let amount: Double
    let description: String
}

struct PortfolioImpactAnalysis {
    let currentAllocation: [String: Double]
    let projectedAllocation: [String: Double]
    let concentrationChange: Double
    let riskChange: Double
    let diversificationImpact: String
}

enum LimitType {
    case tradeCount
    case volume
    case loss
    case none
    
    var displayName: String {
        switch self {
        case .tradeCount: return "Trade Count"
        case .volume: return "Volume"
        case .loss: return "Loss"
        case .none: return "None"
        }
    }
}

struct DailyLimitsCheck {
    let isWithinLimits: Bool
    let isHardLimit: Bool
    let limitType: LimitType
    let currentValue: Double
    let limitValue: Double
}

struct BasicValidationResult {
    let isValid: Bool
    let issues: [ValidationIssue]
}

struct TradeRecord: Identifiable, Codable {
    let id = UUID()
    let orderId: UUID
    let symbol: String
    let side: OrderSide
    let quantity: Double
    let price: Double
    let timestamp: Date
    let fees: Double
    let status: OrderStatus
}

enum OrderStatus: String, CaseIterable, Codable {
    case all = "all"
    case pending = "pending"
    case filled = "filled"
    case cancelled = "cancelled"
    case rejected = "rejected"
    case partiallyFilled = "partially_filled"
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .pending: return "Pending"
        case .filled: return "Filled"
        case .cancelled: return "Cancelled"
        case .rejected: return "Rejected"
        case .partiallyFilled: return "Partially Filled"
        }
    }
    
    var color: Color {
        switch self {
        case .all: return .gray
        case .pending: return .orange
        case .filled: return .green
        case .cancelled: return .gray
        case .rejected: return .red
        case .partiallyFilled: return .blue
        }
    }
}

struct TradeExecutionResult {
    let status: OrderStatus
    let executionPrice: Double
    let fees: Double
    let timestamp: Date
    let message: String?
}

// MARK: - Additional Safety Check Types

struct PDTCheck {
    let isCompliant: Bool
    let reason: String
}

struct MarketHoursCheck {
    let isDuringMarketHours: Bool
    let nextMarketOpen: Date
}

struct ConcentrationCheck {
    let exceedsThreshold: Bool
    let currentPercentage: Double
}

struct VolatilityCheck {
    let isHighVolatility: Bool
    let volatilityLevel: Double
}

struct TradeCosts {
    let commission: Double
    let fees: Double
    let total: Double
}

struct PortfolioImpact {
    let percentageChange: Double
    let newAllocation: [String: Double]
}

// Duplicate definitions removed - using existing ones above
