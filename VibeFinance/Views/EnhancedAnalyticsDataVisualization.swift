//
//  EnhancedAnalyticsDataVisualization.swift
//  VibeFinance - Advanced Analytics Data Visualization
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI
import Charts

// MARK: - Enhanced Analytics Data Visualization

struct EnhancedAnalyticsDataVisualization: View {
    @State private var selectedTimeframe: AnalyticsTimeframe = .oneMonth
    @State private var selectedMetric: AnalyticsMetricType = .portfolioValue
    @State private var showingDetailView = false
    @State private var selectedDataPoint: AnalyticsDataPoint?
    @State private var progressiveDisclosureLevel: DisclosureLevel = .overview

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Progressive Disclosure Header
                    ProgressiveDisclosureHeader(
                        currentLevel: $progressiveDisclosureLevel,
                        selectedTimeframe: $selectedTimeframe,
                        selectedMetric: $selectedMetric
                    )

                    // Main Visualization Area
                    MainVisualizationArea(
                        timeframe: selectedTimeframe,
                        metric: selectedMetric,
                        disclosureLevel: progressiveDisclosureLevel,
                        selectedDataPoint: $selectedDataPoint,
                        onDetailTap: { showingDetailView = true }
                    )

                    // Contextual Insights
                    ContextualInsightsSection(
                        timeframe: selectedTimeframe,
                        metric: selectedMetric,
                        disclosureLevel: progressiveDisclosureLevel
                    )

                    // Benchmark Comparisons
                    BenchmarkComparisonSection(
                        timeframe: selectedTimeframe,
                        metric: selectedMetric
                    )

                    // Progressive Detail Sections
                    if progressiveDisclosureLevel != .overview {
                        AdvancedMetricsSection(
                            disclosureLevel: progressiveDisclosureLevel,
                            timeframe: selectedTimeframe
                        )
                    }

                    if progressiveDisclosureLevel == .expert {
                        ExpertAnalysisSection(timeframe: selectedTimeframe)
                    }
                }
                .padding()
            }
            .background(VibeFinanceDesignSystem.Colors.primaryGradient)
            .navigationTitle("Analytics")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingDetailView) {
                if let dataPoint = selectedDataPoint {
                    DetailedDataPointView(dataPoint: dataPoint, metric: selectedMetric)
                }
            }
        }
    }
}

// MARK: - Progressive Disclosure Header

struct ProgressiveDisclosureHeader: View {
    @Binding var currentLevel: DisclosureLevel
    @Binding var selectedTimeframe: AnalyticsTimeframe
    @Binding var selectedMetric: AnalyticsMetricType

    var body: some View {
        VStack(spacing: 16) {
            // Disclosure Level Selector
            HStack {
                Text("Detail Level")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.8))

                Spacer()

                Picker("Detail Level", selection: $currentLevel) {
                    ForEach(DisclosureLevel.allCases, id: \.self) { level in
                        Text(level.displayName).tag(level)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(maxWidth: 200)
            }

            // Timeframe and Metric Selectors
            HStack(spacing: 12) {
                // Timeframe Selector
                VStack(alignment: .leading, spacing: 4) {
                    Text("Timeframe")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))

                    Picker("Timeframe", selection: $selectedTimeframe) {
                        ForEach(AnalyticsTimeframe.allCases, id: \.self) { timeframe in
                            Text(timeframe.displayName).tag(timeframe)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .foregroundColor(.white)
                }

                Spacer()

                // Metric Selector
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Primary Metric")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))

                    Picker("Metric", selection: $selectedMetric) {
                        ForEach(AnalyticsMetricType.allCases, id: \.self) { metric in
                            Text(metric.displayName).tag(metric)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .foregroundColor(.white)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Main Visualization Area

struct MainVisualizationArea: View {
    let timeframe: AnalyticsTimeframe
    let metric: AnalyticsMetricType
    let disclosureLevel: DisclosureLevel
    @Binding var selectedDataPoint: AnalyticsDataPoint?
    let onDetailTap: () -> Void

    @State private var animationProgress: Double = 0

    var body: some View {
        VStack(spacing: 16) {
            // Chart Header with Context
            ChartHeaderWithContext(
                metric: metric,
                timeframe: timeframe,
                disclosureLevel: disclosureLevel
            )

            // Main Chart
            GeometryReader { geometry in
                ZStack {
                    // Background with subtle grid
                    ChartBackground(geometry: geometry)

                    // Main chart based on disclosure level
                    switch disclosureLevel {
                    case .overview:
                        SimpleLineChart(
                            data: generateMockData(for: metric, timeframe: timeframe),
                            metric: metric,
                            geometry: geometry,
                            selectedPoint: $selectedDataPoint,
                            animationProgress: animationProgress
                        )
                    case .detailed:
                        DetailedMultiLineChart(
                            data: generateMockData(for: metric, timeframe: timeframe),
                            metric: metric,
                            geometry: geometry,
                            selectedPoint: $selectedDataPoint,
                            animationProgress: animationProgress
                        )
                    case .expert:
                        ExpertCandlestickChart(
                            data: generateMockData(for: metric, timeframe: timeframe),
                            metric: metric,
                            geometry: geometry,
                            selectedPoint: $selectedDataPoint,
                            animationProgress: animationProgress
                        )
                    }

                    // Interactive overlay
                    ChartInteractionOverlay(
                        geometry: geometry,
                        selectedPoint: $selectedDataPoint,
                        onDetailTap: onDetailTap
                    )
                }
            }
            .frame(height: chartHeight(for: disclosureLevel))
            .onAppear {
                withAnimation(.easeInOut(duration: 1.5)) {
                    animationProgress = 1.0
                }
            }
            .onChange(of: metric) { _ in
                animationProgress = 0
                withAnimation(.easeInOut(duration: 1.0)) {
                    animationProgress = 1.0
                }
            }

            // Chart Legend and Controls
            ChartLegendAndControls(
                metric: metric,
                disclosureLevel: disclosureLevel,
                selectedPoint: selectedDataPoint
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private func chartHeight(for level: DisclosureLevel) -> CGFloat {
        switch level {
        case .overview: return 200
        case .detailed: return 280
        case .expert: return 350
        }
    }
}

// MARK: - Chart Header with Context

struct ChartHeaderWithContext: View {
    let metric: AnalyticsMetricType
    let timeframe: AnalyticsTimeframe
    let disclosureLevel: DisclosureLevel

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(metric.displayName)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text(metric.description)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                        .lineLimit(2)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text(getCurrentValue(for: metric))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    HStack(spacing: 4) {
                        Image(systemName: getChangeDirection(for: metric))
                            .font(.caption)

                        Text(getChangeValue(for: metric))
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(getChangeColor(for: metric))
                }
            }

            // Contextual baseline information
            if disclosureLevel != .overview {
                BaselineContextView(metric: metric, timeframe: timeframe)
            }
        }
    }

    private func getCurrentValue(for metric: AnalyticsMetricType) -> String {
        switch metric {
        case .portfolioValue: return "$127,450"
        case .totalReturn: return "12.5%"
        case .sharpeRatio: return "1.42"
        case .volatility: return "18.2%"
        case .beta: return "1.15"
        case .maxDrawdown: return "-8.5%"
        }
    }

    private func getChangeDirection(for metric: AnalyticsMetricType) -> String {
        switch metric {
        case .portfolioValue, .totalReturn, .sharpeRatio: return "arrow.up.right"
        case .volatility, .beta, .maxDrawdown: return "arrow.down.right"
        }
    }

    private func getChangeValue(for metric: AnalyticsMetricType) -> String {
        switch metric {
        case .portfolioValue: return "+$2,150 (1.7%)"
        case .totalReturn: return "+2.1%"
        case .sharpeRatio: return "+0.15"
        case .volatility: return "-1.2%"
        case .beta: return "-0.05"
        case .maxDrawdown: return "+1.1%"
        }
    }

    private func getChangeColor(for metric: AnalyticsMetricType) -> Color {
        switch metric {
        case .portfolioValue, .totalReturn, .sharpeRatio: return .green
        case .volatility, .maxDrawdown: return .green // Lower is better
        case .beta: return .orange // Neutral
        }
    }
}

// MARK: - Baseline Context View

struct BaselineContextView: View {
    let metric: AnalyticsMetricType
    let timeframe: AnalyticsTimeframe

    var body: some View {
        HStack(spacing: 16) {
            // Baseline comparison
            VStack(alignment: .leading, spacing: 2) {
                Text("vs Baseline")
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.6))

                Text(getBaselineComparison())
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(getBaselineColor())
            }

            Spacer()

            // Percentile ranking
            VStack(alignment: .trailing, spacing: 2) {
                Text("Percentile")
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.6))

                Text(getPercentileRanking())
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.white.opacity(0.05))
        )
    }

    private func getBaselineComparison() -> String {
        switch metric {
        case .portfolioValue: return "S&P 500: +8.2%"
        case .totalReturn: return "Market Avg: +9.1%"
        case .sharpeRatio: return "Benchmark: 1.15"
        case .volatility: return "Market: 22.1%"
        case .beta: return "Market: 1.00"
        case .maxDrawdown: return "S&P 500: -12.3%"
        }
    }

    private func getBaselineColor() -> Color {
        switch metric {
        case .portfolioValue, .totalReturn: return .green // Outperforming
        case .sharpeRatio: return .green // Better risk-adjusted return
        case .volatility, .maxDrawdown: return .green // Lower volatility is better
        case .beta: return .orange // Slightly higher than market
        }
    }

    private func getPercentileRanking() -> String {
        switch metric {
        case .portfolioValue, .totalReturn: return "78th"
        case .sharpeRatio: return "85th"
        case .volatility: return "65th"
        case .beta: return "72nd"
        case .maxDrawdown: return "81st"
        }
    }
}

// MARK: - Chart Background

struct ChartBackground: View {
    let geometry: GeometryProxy

    var body: some View {
        ZStack {
            // Subtle gradient background
            LinearGradient(
                colors: [
                    Color.white.opacity(0.02),
                    Color.white.opacity(0.01)
                ],
                startPoint: .top,
                endPoint: .bottom
            )

            // Grid lines
            VStack {
                ForEach(0..<5) { i in
                    Rectangle()
                        .fill(Color.white.opacity(0.1))
                        .frame(height: 0.5)
                    if i < 4 { Spacer() }
                }
            }

            HStack {
                ForEach(0..<7) { i in
                    Rectangle()
                        .fill(Color.white.opacity(0.05))
                        .frame(width: 0.5)
                    if i < 6 { Spacer() }
                }
            }
        }
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

// MARK: - Simple Line Chart

struct SimpleLineChart: View {
    let data: [AnalyticsDataPoint]
    let metric: AnalyticsMetricType
    let geometry: GeometryProxy
    @Binding var selectedPoint: AnalyticsDataPoint?
    let animationProgress: Double

    private var minValue: Double {
        data.map { $0.value }.min() ?? 0
    }

    private var maxValue: Double {
        data.map { $0.value }.max() ?? 100
    }

    private var valueRange: Double {
        maxValue - minValue
    }

    var body: some View {
        ZStack {
            // Main line
            Path { path in
                guard !data.isEmpty else { return }

                let width = geometry.size.width
                let height = geometry.size.height

                for (index, point) in data.enumerated() {
                    let x = width * Double(index) / Double(max(data.count - 1, 1))
                    let y = height * (1 - (point.value - minValue) / max(valueRange, 1))

                    if index == 0 {
                        path.move(to: CGPoint(x: x, y: y))
                    } else {
                        path.addLine(to: CGPoint(x: x, y: y))
                    }
                }
            }
            .trim(from: 0, to: animationProgress)
            .stroke(
                LinearGradient(
                    colors: [
                        VibeFinanceDesignSystem.Colors.accentGold,
                        .blue
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                ),
                style: StrokeStyle(lineWidth: 3, lineCap: .round)
            )

            // Area fill
            Path { path in
                guard !data.isEmpty else { return }

                let width = geometry.size.width
                let height = geometry.size.height

                // Start from bottom left
                path.move(to: CGPoint(x: 0, y: height))

                for (index, point) in data.enumerated() {
                    let x = width * Double(index) / Double(max(data.count - 1, 1))
                    let y = height * (1 - (point.value - minValue) / max(valueRange, 1))

                    if index == 0 {
                        path.addLine(to: CGPoint(x: x, y: y))
                    } else {
                        path.addLine(to: CGPoint(x: x, y: y))
                    }
                }

                // Close the path at bottom right
                let lastX = width * Double(data.count - 1) / Double(max(data.count - 1, 1))
                path.addLine(to: CGPoint(x: lastX, y: height))
                path.addLine(to: CGPoint(x: 0, y: height))
            }
            .trim(from: 0, to: animationProgress)
            .fill(
                LinearGradient(
                    colors: [
                        VibeFinanceDesignSystem.Colors.accentGold.opacity(0.3),
                        Color.blue.opacity(0.1)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )

            // Data points
            ForEach(Array(data.enumerated()), id: \.offset) { index, point in
                let x = geometry.size.width * Double(index) / Double(max(data.count - 1, 1))
                let y = geometry.size.height * (1 - (point.value - minValue) / max(valueRange, 1))

                Circle()
                    .fill(selectedPoint?.id == point.id ? .white : VibeFinanceDesignSystem.Colors.accentGold)
                    .frame(width: selectedPoint?.id == point.id ? 8 : 4, height: selectedPoint?.id == point.id ? 8 : 4)
                    .position(x: x, y: y)
                    .opacity(animationProgress)
                    .scaleEffect(selectedPoint?.id == point.id ? 1.5 : 1.0)
                    .animation(.spring(response: 0.3), value: selectedPoint?.id)
                    .onTapGesture {
                        selectedPoint = point
                    }
            }
        }
    }
}

// MARK: - Detailed Multi-Line Chart

struct DetailedMultiLineChart: View {
    let data: [AnalyticsDataPoint]
    let metric: AnalyticsMetricType
    let geometry: GeometryProxy
    @Binding var selectedPoint: AnalyticsDataPoint?
    let animationProgress: Double

    var body: some View {
        ZStack {
            // Primary metric line (same as simple chart)
            SimpleLineChart(
                data: data,
                metric: metric,
                geometry: geometry,
                selectedPoint: $selectedPoint,
                animationProgress: animationProgress
            )

            // Benchmark comparison line
            BenchmarkComparisonLine(
                data: generateBenchmarkData(),
                geometry: geometry,
                animationProgress: animationProgress
            )

            // Moving average line
            MovingAverageLine(
                data: calculateMovingAverage(data),
                geometry: geometry,
                animationProgress: animationProgress
            )

            // Volatility bands
            VolatilityBands(
                data: data,
                geometry: geometry,
                animationProgress: animationProgress
            )
        }
    }

    private func generateBenchmarkData() -> [AnalyticsDataPoint] {
        // Generate benchmark data (e.g., S&P 500)
        return data.enumerated().map { index, point in
            AnalyticsDataPoint(
                id: UUID(),
                date: point.date,
                value: point.value * (0.95 + Double.random(in: -0.1...0.1)),
                volume: point.volume,
                metadata: ["type": "benchmark"]
            )
        }
    }

    private func calculateMovingAverage(_ data: [AnalyticsDataPoint]) -> [AnalyticsDataPoint] {
        let period = 20
        return data.enumerated().compactMap { index, point in
            guard index >= period - 1 else { return nil }

            let startIndex = max(0, index - period + 1)
            let endIndex = index
            let subset = Array(data[startIndex...endIndex])
            let average = subset.map { $0.value }.reduce(0, +) / Double(subset.count)

            return AnalyticsDataPoint(
                id: UUID(),
                date: point.date,
                value: average,
                volume: point.volume,
                metadata: ["type": "moving_average"]
            )
        }
    }
}

// MARK: - Expert Candlestick Chart

struct ExpertCandlestickChart: View {
    let data: [AnalyticsDataPoint]
    let metric: AnalyticsMetricType
    let geometry: GeometryProxy
    @Binding var selectedPoint: AnalyticsDataPoint?
    let animationProgress: Double

    var body: some View {
        ZStack {
            // Candlestick bodies and wicks
            ForEach(Array(data.enumerated()), id: \.offset) { index, point in
                CandlestickView(
                    dataPoint: point,
                    index: index,
                    totalCount: data.count,
                    geometry: geometry,
                    minValue: data.map { $0.value }.min() ?? 0,
                    maxValue: data.map { $0.value }.max() ?? 100,
                    animationProgress: animationProgress,
                    isSelected: selectedPoint?.id == point.id
                )
                .onTapGesture {
                    selectedPoint = point
                }
            }

            // Volume bars at bottom
            VolumeChart(
                data: data,
                geometry: geometry,
                animationProgress: animationProgress
            )

            // Technical indicators
            TechnicalIndicators(
                data: data,
                geometry: geometry,
                animationProgress: animationProgress
            )
        }
    }
}

// MARK: - Supporting Chart Components

struct BenchmarkComparisonLine: View {
    let data: [AnalyticsDataPoint]
    let geometry: GeometryProxy
    let animationProgress: Double

    private var minValue: Double {
        data.map { $0.value }.min() ?? 0
    }

    private var maxValue: Double {
        data.map { $0.value }.max() ?? 100
    }

    private var valueRange: Double {
        maxValue - minValue
    }

    var body: some View {
        Path { path in
            guard !data.isEmpty else { return }

            let width = geometry.size.width
            let height = geometry.size.height

            for (index, point) in data.enumerated() {
                let x = width * Double(index) / Double(max(data.count - 1, 1))
                let y = height * (1 - (point.value - minValue) / max(valueRange, 1))

                if index == 0 {
                    path.move(to: CGPoint(x: x, y: y))
                } else {
                    path.addLine(to: CGPoint(x: x, y: y))
                }
            }
        }
        .trim(from: 0, to: animationProgress)
        .stroke(
            Color.gray.opacity(0.6),
            style: StrokeStyle(lineWidth: 2, dash: [5, 3])
        )
    }
}

struct MovingAverageLine: View {
    let data: [AnalyticsDataPoint]
    let geometry: GeometryProxy
    let animationProgress: Double

    private var minValue: Double {
        data.map { $0.value }.min() ?? 0
    }

    private var maxValue: Double {
        data.map { $0.value }.max() ?? 100
    }

    private var valueRange: Double {
        maxValue - minValue
    }

    var body: some View {
        Path { path in
            guard !data.isEmpty else { return }

            let width = geometry.size.width
            let height = geometry.size.height

            for (index, point) in data.enumerated() {
                let x = width * Double(index) / Double(max(data.count - 1, 1))
                let y = height * (1 - (point.value - minValue) / max(valueRange, 1))

                if index == 0 {
                    path.move(to: CGPoint(x: x, y: y))
                } else {
                    path.addLine(to: CGPoint(x: x, y: y))
                }
            }
        }
        .trim(from: 0, to: animationProgress)
        .stroke(
            Color.orange.opacity(0.8),
            style: StrokeStyle(lineWidth: 1.5)
        )
    }
}

// MARK: - Data Generation Helper

func generateMockData(for metric: AnalyticsMetricType, timeframe: AnalyticsTimeframe) -> [AnalyticsDataPoint] {
    return AnalyticsDataPoint.generateMockData(for: metric, timeframe: timeframe)
}