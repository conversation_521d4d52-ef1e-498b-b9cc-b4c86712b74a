//
//  PaymentComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import StoreKit

// MARK: - Subscription Plan Card
struct SubscriptionPlanCard: View {
    let tier: SubscriptionTier
    let billingCycle: BillingCycle
    let isSelected: Bool
    let onSelect: () -> Void
    
    private var price: Double {
        switch billingCycle {
        case .monthly: return tier.monthlyPrice
        case .yearly: return tier.yearlyPrice / 12
        case .lifetime: return 0 // One-time payment
        }
    }
    
    private var totalPrice: Double {
        switch billingCycle {
        case .monthly: return tier.monthlyPrice
        case .yearly: return tier.yearlyPrice
        case .lifetime: return tier.lifetimePrice
        }
    }

    private var tierDescription: String {
        switch tier {
        case .free: return "Perfect for getting started"
        case .basic: return "Great for regular users"
        case .pro: return "Best for power users"
        }
    }
    
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 16) {
                headerSection
                savingsSection
                featuresSection
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? Color.purple : Color(.systemGray4), lineWidth: 2)
                    )
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(tier.displayName)
                        .font(.title2)
                        .fontWeight(.bold)

                    if tier.popularBadge {
                        popularBadge
                    }
                }

                Text(tierDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            priceSection
        }
    }

    private var popularBadge: some View {
        Text("POPULAR")
            .font(.caption)
            .fontWeight(.bold)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.orange)
            )
    }

    private var priceSection: some View {
        VStack(alignment: .trailing, spacing: 2) {
            if billingCycle == .lifetime {
                Text(String(format: "$%.0f", totalPrice))
                    .font(.title2)
                    .fontWeight(.bold)
                Text("one-time")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                Text(String(format: "$%.2f", price))
                    .font(.title2)
                    .fontWeight(.bold)
                Text("per month")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }

    private var savingsSection: some View {
        Group {
            if billingCycle != .monthly && tier != .free {
                HStack {
                    Text(tier.savings)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                    Spacer()
                }
            }
        }
    }

    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(tier.features, id: \.self) { feature in
                HStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.green)

                    Text(feature)
                        .font(.caption)
                        .foregroundColor(.primary)

                    Spacer()
                }
            }
        }
    }
}

// MARK: - Feature Comparison Row
struct FeatureComparisonRow: View {
    let feature: String
    let free: String
    let basic: String
    let pro: String
    
    var body: some View {
        HStack {
            Text(feature)
                .font(.caption)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Text(free)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 60)
            
            Text(basic)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 60)
            
            Text(pro)
                .font(.caption)
                .foregroundColor(.purple)
                .fontWeight(.semibold)
                .frame(width: 60)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Payment Sheet
struct PaymentSheet: View {
    let selectedTier: SubscriptionTier
    let billingCycle: BillingCycle
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var paymentManager: PaymentManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @State private var selectedPaymentMethod: PaymentMethod?
    @State private var showingAddPaymentMethod = false
    @State private var agreedToTerms = false
    @State private var showingSuccessAnimation = false
    @State private var paymentProgress: Double = 0.0
    @State private var showingErrorAlert = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Order Summary
                    orderSummary
                    
                    // Payment Methods
                    paymentMethodsSection
                    
                    // Terms and Conditions
                    termsSection
                    
                    // Purchase Button
                    purchaseButton
                }
                .padding()
            }
            .navigationTitle("Complete Purchase")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingAddPaymentMethod) {
                AddPaymentMethodView()
            }
            .onAppear {
                Task {
                    await paymentManager.loadPaymentMethods()
                }
            }
        }
    }
    
    private var orderSummary: some View {
        VStack(spacing: 16) {
            Text("Order Summary")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                HStack {
                    Text("\(selectedTier.displayName) Plan")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Spacer()
                    Text(String(format: "$%.2f", selectedTier.monthlyPrice))
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                
                if billingCycle == .yearly {
                    HStack {
                        Text("Yearly Discount (20%)")
                            .font(.subheadline)
                            .foregroundColor(.green)
                        Spacer()
                        Text(String(format: "-$%.2f", selectedTier.monthlyPrice * 12 * 0.2))
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                }
                
                if let discount = paymentManager.appliedDiscount {
                    HStack {
                        Text("Promo Code (\(discount.code))")
                            .font(.subheadline)
                            .foregroundColor(.green)
                        Spacer()
                        Text("-\(discount.formattedValue)")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                }
                
                Divider()
                
                HStack {
                    Text("Total")
                        .font(.headline)
                        .fontWeight(.bold)
                    Spacer()
                    Text(totalPriceText)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var paymentMethodsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Payment Method")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Button("Add New") {
                    showingAddPaymentMethod = true
                }
                .font(.subheadline)
                .foregroundColor(.purple)
            }
            
            if paymentManager.paymentMethods.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "creditcard")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    Text("No payment methods added")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Button("Add Payment Method") {
                        showingAddPaymentMethod = true
                    }
                    .font(.subheadline)
                    .foregroundColor(.purple)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
            } else {
                VStack(spacing: 8) {
                    ForEach(paymentManager.paymentMethods) { method in
                        PaymentMethodRow(
                            method: method,
                            isSelected: selectedPaymentMethod?.id == method.id,
                            onSelect: {
                                selectedPaymentMethod = method
                            }
                        )
                    }
                }
            }
        }
    }
    
    private var termsSection: some View {
        VStack(spacing: 12) {
            // Agreement checkbox
            HStack(spacing: 12) {
                Button(action: {
                    agreedToTerms.toggle()
                }) {
                    Image(systemName: agreedToTerms ? "checkmark.square.fill" : "square")
                        .foregroundColor(agreedToTerms ? .purple : .gray)
                        .font(.title3)
                }

                Text("I agree to the Terms of Service and Privacy Policy")
                    .font(.subheadline)
                    .foregroundColor(.primary)

                Spacer()
            }

            Text("Your subscription will automatically renew unless cancelled at least 24 hours before the end of the current period.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            HStack(spacing: 16) {
                Button("Terms of Service") {
                    // Open terms
                }
                .font(.caption)
                .foregroundColor(.purple)

                Button("Privacy Policy") {
                    // Open privacy policy
                }
                .font(.caption)
                .foregroundColor(.purple)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    private var purchaseButton: some View {
        VStack(spacing: 12) {
            // Progress indicator when processing
            if paymentManager.isProcessingPayment {
                VStack(spacing: 8) {
                    ProgressView(value: paymentProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .purple))

                    Text("Processing payment...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Button(action: {
                Task {
                    await processPurchase()
                }
            }) {
                HStack {
                    if paymentManager.isProcessingPayment {
                        ProgressView()
                            .scaleEffect(0.8)
                            .foregroundColor(.white)
                    } else if showingSuccessAnimation {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.white)
                    }

                    Text(buttonText)
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(buttonBackgroundColor)
                        .scaleEffect(showingSuccessAnimation ? 1.05 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: showingSuccessAnimation)
                )
            }
            .disabled(!canPurchase || paymentManager.isProcessingPayment)
        }
        .alert("Payment Error", isPresented: $showingErrorAlert) {
            Button("OK") { }
        } message: {
            Text(paymentManager.paymentError?.localizedDescription ?? "An error occurred during payment processing.")
        }
    }
    
    private var canPurchase: Bool {
        return agreedToTerms && (!paymentManager.paymentMethods.isEmpty || selectedPaymentMethod != nil)
    }

    private var buttonText: String {
        if paymentManager.isProcessingPayment {
            return "Processing..."
        } else if showingSuccessAnimation {
            return "Success!"
        } else {
            return "Subscribe to \(selectedTier.displayName) - \(totalPriceText)"
        }
    }

    private var buttonBackgroundColor: Color {
        if showingSuccessAnimation {
            return .green
        } else if canPurchase && !paymentManager.isProcessingPayment {
            return .purple
        } else {
            return .gray
        }
    }
    
    private var totalPriceText: String {
        let basePrice = billingCycle == .yearly ? selectedTier.yearlyPrice : selectedTier.monthlyPrice
        // Apply discounts here
        return String(format: "$%.2f", basePrice)
    }
    
    private func processPurchase() async {
        guard let product = subscriptionManager.getProduct(for: selectedTier) else { return }

        // Animate progress
        await MainActor.run {
            paymentProgress = 0.0
        }

        // Simulate progress steps
        for progress in stride(from: 0.0, through: 0.8, by: 0.2) {
            await MainActor.run {
                paymentProgress = progress
            }
            try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        }

        let result = await paymentManager.processPayment(for: product, paymentMethod: selectedPaymentMethod)

        await MainActor.run {
            paymentProgress = 1.0
        }

        switch result {
        case .success:
            await MainActor.run {
                showingSuccessAnimation = true
            }

            // Show success animation for 1 second
            try? await Task.sleep(nanoseconds: 1_000_000_000)

            await MainActor.run {
                dismiss()
            }

        case .cancelled:
            // User cancelled, reset progress
            await MainActor.run {
                paymentProgress = 0.0
            }

        case .pending:
            // Show pending message
            await MainActor.run {
                paymentProgress = 0.5
            }

        case .failed(let _):
            await MainActor.run {
                paymentProgress = 0.0
                showingErrorAlert = true
            }
        }
    }
}

// MARK: - Payment Method Row
struct PaymentMethodRow: View {
    let method: PaymentMethod
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 12) {
                Image(systemName: method.displayIcon)
                    .font(.title3)
                    .foregroundColor(.purple)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(method.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    if let lastFour = method.lastFour {
                        Text("•••• \(lastFour)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if method.isDefault {
                    Text("DEFAULT")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.purple.opacity(0.1))
                        )
                }
                
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.title3)
                    .foregroundColor(isSelected ? .purple : .gray)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.purple.opacity(0.1) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.purple : Color.clear, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Add Payment Method View
struct AddPaymentMethodView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var paymentManager: PaymentManager
    @State private var selectedType: PaymentMethodType = .creditCard
    @State private var cardNumber = ""
    @State private var expiryDate = ""
    @State private var cvv = ""
    @State private var cardholderName = ""
    @State private var isDefault = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Payment Method Type Selector
                    paymentTypeSelector
                    
                    // Payment Method Form
                    switch selectedType {
                    case .creditCard:
                        creditCardForm
                    case .applePay:
                        applePaySection
                    case .bankTransfer:
                        bankTransferForm
                    }
                    
                    // Add Button
                    addButton
                }
                .padding()
            }
            .navigationTitle("Add Payment Method")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var paymentTypeSelector: some View {
        VStack(spacing: 12) {
            Text("Payment Method Type")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 12) {
                ForEach(PaymentMethodType.allCases, id: \.self) { type in
                    Button(action: {
                        selectedType = type
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: type == .creditCard ? "creditcard.fill" : type == .applePay ? "apple.logo" : "building.columns.fill")
                                .font(.title2)
                            
                            Text(type.displayName)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(selectedType == type ? .white : .primary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedType == type ? Color.purple : Color(.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
    
    private var creditCardForm: some View {
        VStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Cardholder Name")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField("John Doe", text: $cardholderName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Card Number")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField("1234 5678 9012 3456", text: $cardNumber)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.numberPad)
            }
            
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Expiry Date")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    TextField("MM/YY", text: $expiryDate)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.numberPad)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("CVV")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    TextField("123", text: $cvv)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.numberPad)
                }
            }
            
            Toggle("Set as default payment method", isOn: $isDefault)
                .font(.subheadline)
        }
    }
    
    private var applePaySection: some View {
        VStack(spacing: 16) {
            Image(systemName: "apple.logo")
                .font(.system(size: 60))
                .foregroundColor(.black)
            
            Text("Apple Pay")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Use Touch ID or Face ID to pay securely with Apple Pay")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
    
    private var bankTransferForm: some View {
        VStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Bank Name")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField("Chase Bank", text: $cardholderName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Account Number")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField("*********", text: $cardNumber)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.numberPad)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Routing Number")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                TextField("*********", text: $expiryDate)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.numberPad)
            }
        }
    }
    
    private var addButton: some View {
        Button(action: {
            Task {
                await addPaymentMethod()
            }
        }) {
            Text("Add Payment Method")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isFormValid ? Color.purple : Color.gray)
                )
        }
        .disabled(!isFormValid)
    }
    
    private var isFormValid: Bool {
        switch selectedType {
        case .creditCard:
            return !cardholderName.isEmpty && !cardNumber.isEmpty && !expiryDate.isEmpty && !cvv.isEmpty
        case .applePay:
            return true
        case .bankTransfer:
            return !cardholderName.isEmpty && !cardNumber.isEmpty && !expiryDate.isEmpty
        }
    }
    
    private func addPaymentMethod() async {
        let method = PaymentMethod(
            id: UUID().uuidString,
            type: selectedType,
            displayName: selectedType == .creditCard ? "•••• \(String(cardNumber.suffix(4)))" : selectedType.displayName,
            lastFour: selectedType == .creditCard ? String(cardNumber.suffix(4)) : nil,
            expiryDate: selectedType == .creditCard ? expiryDate : nil,
            isDefault: isDefault,
            bankDetails: selectedType == .bankTransfer ? BankDetails(
                accountNumber: cardNumber,
                routingNumber: expiryDate,
                accountType: .checking,
                bankName: cardholderName
            ) : nil,
            createdAt: Date()
        )
        
        let success = await paymentManager.addPaymentMethod(method)
        if success {
            dismiss()
        }
    }
}


