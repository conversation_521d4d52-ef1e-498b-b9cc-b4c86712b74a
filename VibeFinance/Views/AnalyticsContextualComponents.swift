//
//  AnalyticsContextualComponents.swift
//  VibeFinance - Contextual Analytics Components
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI
import Charts

// MARK: - Contextual Insights Section

struct ContextualInsightsSection: View {
    let timeframe: AnalyticsTimeframe
    let metric: AnalyticsMetricType
    let disclosureLevel: DisclosureLevel
    
    var body: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                    .font(.title3)
                
                Text("Key Insights")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("Warren's Wisdom")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.white.opacity(0.1))
                    )
            }
            
            // Insights Grid
            LazyVGrid(columns: gridColumns(for: disclosureLevel), spacing: 12) {
                ForEach(getInsights(), id: \.id) { insight in
                    InsightCard(insight: insight, disclosureLevel: disclosureLevel)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private func gridColumns(for level: DisclosureLevel) -> [GridItem] {
        switch level {
        case .overview:
            return [GridItem(.flexible()), GridItem(.flexible())]
        case .detailed:
            return [GridItem(.flexible()), GridItem(.flexible()), GridItem(.flexible())]
        case .expert:
            return [GridItem(.flexible()), GridItem(.flexible())]
        }
    }
    
    private func getInsights() -> [AnalyticsInsight] {
        switch (metric, disclosureLevel) {
        case (.portfolioValue, .overview):
            return [
                AnalyticsInsight(
                    id: UUID(),
                    title: "Strong Performance",
                    description: "Your portfolio outperformed the S&P 500 by 3.4% this period",
                    type: .positive,
                    confidence: 0.92,
                    buffettQuote: "Time is the friend of the wonderful business."
                ),
                AnalyticsInsight(
                    id: UUID(),
                    title: "Diversification",
                    description: "Well-balanced across 8 sectors with no single holding >15%",
                    type: .neutral,
                    confidence: 0.88,
                    buffettQuote: "Diversification is protection against ignorance."
                )
            ]
        case (.portfolioValue, .detailed):
            return [
                AnalyticsInsight(
                    id: UUID(),
                    title: "Risk-Adjusted Returns",
                    description: "Sharpe ratio of 1.42 indicates excellent risk-adjusted performance",
                    type: .positive,
                    confidence: 0.95,
                    buffettQuote: "Risk comes from not knowing what you're doing."
                ),
                AnalyticsInsight(
                    id: UUID(),
                    title: "Volatility Analysis",
                    description: "18.2% volatility is below market average of 22.1%",
                    type: .positive,
                    confidence: 0.87,
                    buffettQuote: "Volatility is far from synonymous with risk."
                ),
                AnalyticsInsight(
                    id: UUID(),
                    title: "Drawdown Control",
                    description: "Maximum drawdown of 8.5% shows good downside protection",
                    type: .positive,
                    confidence: 0.91,
                    buffettQuote: "Rule No. 1: Never lose money. Rule No. 2: Never forget rule No. 1."
                )
            ]
        case (.portfolioValue, .expert):
            return [
                AnalyticsInsight(
                    id: UUID(),
                    title: "Alpha Generation",
                    description: "Generated 2.8% alpha vs benchmark with 95% confidence",
                    type: .positive,
                    confidence: 0.95,
                    buffettQuote: "Price is what you pay. Value is what you get."
                ),
                AnalyticsInsight(
                    id: UUID(),
                    title: "Factor Exposure",
                    description: "Strong value and quality factor tilts align with long-term strategy",
                    type: .positive,
                    confidence: 0.89,
                    buffettQuote: "It's far better to buy a wonderful company at a fair price."
                )
            ]
        default:
            return getDefaultInsights()
        }
    }
    
    private func getDefaultInsights() -> [AnalyticsInsight] {
        return [
            AnalyticsInsight(
                id: UUID(),
                title: "Performance Trend",
                description: "Consistent upward trajectory over the selected timeframe",
                type: .positive,
                confidence: 0.85,
                buffettQuote: "Someone's sitting in the shade today because someone planted a tree a long time ago."
            )
        ]
    }
}

// MARK: - Insight Card

struct InsightCard: View {
    let insight: AnalyticsInsight
    let disclosureLevel: DisclosureLevel
    @State private var showingDetail = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header with confidence indicator
            HStack {
                Image(systemName: insight.type.iconName)
                    .foregroundColor(insight.type.color)
                    .font(.subheadline)
                
                Text(insight.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                Spacer()
                
                if disclosureLevel != .overview {
                    ConfidenceIndicator(confidence: insight.confidence)
                }
            }
            
            // Description
            Text(insight.description)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
                .lineLimit(disclosureLevel == .overview ? 2 : 3)
            
            // Buffett quote (for detailed and expert levels)
            if disclosureLevel != .overview {
                Divider()
                    .background(Color.white.opacity(0.2))
                
                HStack(spacing: 4) {
                    Image(systemName: "quote.bubble")
                        .font(.caption2)
                        .foregroundColor(.yellow)
                    
                    Text(insight.buffettQuote)
                        .font(.caption2)
                        .fontStyle(.italic)
                        .foregroundColor(.white.opacity(0.7))
                        .lineLimit(2)
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(insight.type.backgroundColor)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(insight.type.borderColor, lineWidth: 1)
                )
        )
        .onTapGesture {
            if disclosureLevel == .expert {
                showingDetail = true
            }
        }
        .sheet(isPresented: $showingDetail) {
            DetailedInsightView(insight: insight)
        }
    }
}

// MARK: - Confidence Indicator

struct ConfidenceIndicator: View {
    let confidence: Double
    
    var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<5) { index in
                Circle()
                    .fill(index < Int(confidence * 5) ? .green : Color.white.opacity(0.3))
                    .frame(width: 4, height: 4)
            }
        }
    }
}

// MARK: - Benchmark Comparison Section

struct BenchmarkComparisonSection: View {
    let timeframe: AnalyticsTimeframe
    let metric: AnalyticsMetricType
    
    var body: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Image(systemName: "chart.bar.xaxis")
                    .foregroundColor(.blue)
                    .font(.title3)
                
                Text("Benchmark Comparison")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text(timeframe.displayName)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.white.opacity(0.1))
                    )
            }
            
            // Comparison Chart
            BenchmarkComparisonChart(metric: metric, timeframe: timeframe)
            
            // Performance Metrics
            BenchmarkMetricsGrid(metric: metric, timeframe: timeframe)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Benchmark Comparison Chart

struct BenchmarkComparisonChart: View {
    let metric: AnalyticsMetricType
    let timeframe: AnalyticsTimeframe
    
    var body: some View {
        HStack(spacing: 20) {
            // Portfolio Performance
            VStack(spacing: 8) {
                Text("Your Portfolio")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                
                VStack(spacing: 4) {
                    Text(getPortfolioValue())
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.up.right")
                            .font(.caption)
                        
                        Text(getPortfolioChange())
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.green)
                }
                
                // Performance bar
                PerformanceBar(
                    value: getPortfolioPerformance(),
                    color: .green,
                    maxValue: 20.0
                )
            }
            
            Divider()
                .background(Color.white.opacity(0.3))
            
            // Benchmark Performance
            VStack(spacing: 8) {
                Text("S&P 500")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                
                VStack(spacing: 4) {
                    Text(getBenchmarkValue())
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.up.right")
                            .font(.caption)
                        
                        Text(getBenchmarkChange())
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.blue)
                }
                
                // Performance bar
                PerformanceBar(
                    value: getBenchmarkPerformance(),
                    color: .blue,
                    maxValue: 20.0
                )
            }
        }
        .frame(height: 120)
    }
    
    private func getPortfolioValue() -> String {
        switch metric {
        case .portfolioValue: return "$127,450"
        case .totalReturn: return "12.5%"
        case .sharpeRatio: return "1.42"
        case .volatility: return "18.2%"
        case .beta: return "1.15"
        case .maxDrawdown: return "-8.5%"
        }
    }
    
    private func getPortfolioChange() -> String {
        return "+3.4%"
    }
    
    private func getPortfolioPerformance() -> Double {
        return 12.5
    }
    
    private func getBenchmarkValue() -> String {
        switch metric {
        case .portfolioValue: return "$120,000"
        case .totalReturn: return "9.1%"
        case .sharpeRatio: return "1.15"
        case .volatility: return "22.1%"
        case .beta: return "1.00"
        case .maxDrawdown: return "-12.3%"
        }
    }
    
    private func getBenchmarkChange() -> String {
        return "+1.8%"
    }
    
    private func getBenchmarkPerformance() -> Double {
        return 9.1
    }
}

// MARK: - Performance Bar

struct PerformanceBar: View {
    let value: Double
    let color: Color
    let maxValue: Double
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // Background
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.white.opacity(0.2))
                    .frame(height: 8)
                
                // Progress
                RoundedRectangle(cornerRadius: 4)
                    .fill(color)
                    .frame(
                        width: geometry.size.width * (value / maxValue),
                        height: 8
                    )
            }
        }
        .frame(height: 8)
    }
}
