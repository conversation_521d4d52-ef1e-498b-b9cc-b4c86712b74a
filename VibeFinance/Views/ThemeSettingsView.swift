import SwiftUI

struct ThemeSettingsView: View {
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.theme) var theme
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                VStack(spacing: 16) {
                    Text("Appearance")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onBackground)
                    
                    Text("Customize how VibeFinance looks")
                        .font(.subheadline)
                        .foregroundColor(theme.onBackground.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 20)
                .padding(.horizontal, 20)
                
                ScrollView {
                    VStack(spacing: 24) {
                        // System Theme Toggle
                        systemThemeSection
                        
                        // Theme Selection
                        if !themeManager.useSystemTheme {
                            themeSelectionSection
                        }
                        
                        // Preview Section
                        previewSection
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 30)
                }
                
                Spacer()
            }
            .background(theme.background.ignoresSafeArea())
            .navigationBarHidden(true)
            .overlay(
                // Close Button
                VStack {
                    HStack {
                        Spacer()
                        Button(action: { dismiss() }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title2)
                                .foregroundColor(theme.onBackground.opacity(0.6))
                                .background(Circle().fill(theme.surface))
                        }
                        .padding(.trailing, 20)
                        .padding(.top, 20)
                    }
                    Spacer()
                }
            )
        }
    }
    
    private var systemThemeSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("System Theme")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(theme.onBackground)
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Follow System")
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(theme.onBackground)
                    
                    Text("Automatically switch between light and dark themes based on your device settings")
                        .font(.caption)
                        .foregroundColor(theme.onBackground.opacity(0.7))
                }
                
                Spacer()
                
                Toggle("", isOn: $themeManager.useSystemTheme)
                    .toggleStyle(SwitchToggleStyle(tint: theme.accent))
                    .onChange(of: themeManager.useSystemTheme) { _, newValue in
                        if newValue {
                            themeManager.toggleSystemTheme()
                        }
                    }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(theme.surface)
                    .shadow(color: theme.onBackground.opacity(0.1), radius: 2, x: 0, y: 1)
            )
        }
    }
    
    private var themeSelectionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Theme")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(theme.onBackground)
            
            HStack(spacing: 16) {
                // Light Theme Option
                ThemeOptionCard(
                    theme: .light,
                    isSelected: themeManager.currentTheme == .light,
                    onSelect: { themeManager.setTheme(.light) }
                )
                
                // Dark Theme Option
                ThemeOptionCard(
                    theme: .dark,
                    isSelected: themeManager.currentTheme == .dark,
                    onSelect: { themeManager.setTheme(.dark) }
                )
            }
        }
    }
    
    private var previewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Preview")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(theme.onBackground)
            
            // Preview Card
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Portfolio Value")
                            .font(.caption)
                            .foregroundColor(theme.onSurface.opacity(0.7))
                        
                        Text("$125,847.32")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(theme.onSurface)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("+$2,847.32")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(theme.success)
                        
                        Text("+2.31%")
                            .font(.caption)
                            .foregroundColor(theme.success)
                    }
                }
                
                HStack(spacing: 12) {
                    Button("Buy") {
                        // Preview action
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(theme.accent)
                    .cornerRadius(8)

                    Button("Sell") {
                        // Preview action
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(theme.onSurface)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(theme.onSurface.opacity(0.3), lineWidth: 1)
                    )
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(theme.surface)
                    .shadow(color: theme.onBackground.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
    }
}

struct ThemeOptionCard: View {
    let theme: AppTheme
    let isSelected: Bool
    let onSelect: () -> Void
    @Environment(\.theme) var currentTheme
    
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 12) {
                // Theme Preview
                ZStack {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(theme == .dark ? Color.black : Color.white)
                        .frame(height: 60)
                    
                    VStack(spacing: 4) {
                        HStack(spacing: 4) {
                            Circle()
                                .fill(Color(red: 1.0, green: 0.84, blue: 0.0))
                                .frame(width: 8, height: 8)
                            
                            Rectangle()
                                .fill(theme == .dark ? Color.white.opacity(0.8) : Color.black.opacity(0.8))
                                .frame(width: 20, height: 2)
                                .cornerRadius(1)
                        }
                        
                        Rectangle()
                            .fill(theme == .dark ? Color.white.opacity(0.6) : Color.black.opacity(0.6))
                            .frame(width: 30, height: 2)
                            .cornerRadius(1)
                    }
                }
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isSelected ? currentTheme.accent : Color.clear, lineWidth: 2)
                )
                
                Text(theme.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(currentTheme.onBackground)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(maxWidth: .infinity)
    }
}



#Preview {
    ThemeSettingsView()
        .environmentObject(ThemeManager())
        .environment(\.theme, ThemeColors.dark)
}
