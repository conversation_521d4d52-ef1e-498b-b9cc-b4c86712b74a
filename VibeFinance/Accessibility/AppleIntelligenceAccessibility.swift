//
//  AppleIntelligenceAccessibility.swift
//  VibeFinance - Apple Intelligence Accessibility Features
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI
import Accessibility

// MARK: - Apple Intelligence Accessibility Manager

@MainActor
class AppleIntelligenceAccessibilityManager: ObservableObject {
    static let shared = AppleIntelligenceAccessibilityManager()
    
    @Published var isVoiceOverEnabled = false
    @Published var isDynamicTypeEnabled = false
    @Published var isReduceMotionEnabled = false
    @Published var isHighContrastEnabled = false
    @Published var preferredContentSizeCategory: ContentSizeCategory = .medium
    
    private init() {
        setupAccessibilityObservers()
        updateAccessibilitySettings()
    }
    
    private func setupAccessibilityObservers() {
        NotificationCenter.default.addObserver(
            forName: UIAccessibility.voiceOverStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.updateAccessibilitySettings()
        }
        
        NotificationCenter.default.addObserver(
            forName: UIContentSizeCategory.didChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.updateAccessibilitySettings()
        }
        
        NotificationCenter.default.addObserver(
            forName: UIAccessibility.reduceMotionStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.updateAccessibilitySettings()
        }
    }
    
    private func updateAccessibilitySettings() {
        isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
        isDynamicTypeEnabled = UIApplication.shared.preferredContentSizeCategory.isAccessibilityCategory
        isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
        isHighContrastEnabled = UIAccessibility.isDarkerSystemColorsEnabled
        preferredContentSizeCategory = ContentSizeCategory(UIApplication.shared.preferredContentSizeCategory)
    }
    
    // MARK: - Accessibility Helpers
    
    func accessibilityLabel(for aiResponse: String) -> String {
        return "Apple Intelligence response: \(aiResponse)"
    }
    
    func accessibilityHint(for action: String) -> String {
        return "Double tap to \(action)"
    }
    
    func accessibilityValue(for portfolioValue: Double, change: Double) -> String {
        let changeText = change >= 0 ? "increased" : "decreased"
        return "Portfolio value $\(String(format: "%.2f", portfolioValue)), \(changeText) by $\(String(format: "%.2f", abs(change)))"
    }
}

// MARK: - Accessibility View Modifiers

struct AppleIntelligenceAccessibilityModifier: ViewModifier {
    @StateObject private var accessibilityManager = AppleIntelligenceAccessibilityManager.shared
    
    func body(content: Content) -> some View {
        content
            .dynamicTypeSize(accessibilityManager.isDynamicTypeEnabled ? .accessibility5 : .medium)
            .animation(
                accessibilityManager.isReduceMotionEnabled ? .none : .easeInOut(duration: 0.3),
                value: accessibilityManager.isReduceMotionEnabled
            )
            .preferredColorScheme(accessibilityManager.isHighContrastEnabled ? .dark : nil)
    }
}

// MARK: - Voice Control Support

struct VoiceControlSupport: ViewModifier {
    let identifier: String
    let label: String
    
    func body(content: Content) -> some View {
        content
            .accessibilityIdentifier(identifier)
            .accessibilityLabel(label)
            .accessibilityAddTraits(.isButton)
    }
}

// MARK: - Smart Haptic Feedback

class SmartHapticManager: ObservableObject {
    static let shared = SmartHapticManager()
    
    private let impactLight = UIImpactFeedbackGenerator(style: .light)
    private let impactMedium = UIImpactFeedbackGenerator(style: .medium)
    private let impactHeavy = UIImpactFeedbackGenerator(style: .heavy)
    private let notification = UINotificationFeedbackGenerator()
    
    private init() {
        prepareHaptics()
    }
    
    private func prepareHaptics() {
        impactLight.prepare()
        impactMedium.prepare()
        impactHeavy.prepare()
        notification.prepare()
    }
    
    func playAIResponseHaptic() {
        guard !UIAccessibility.isReduceMotionEnabled else { return }
        impactLight.impactOccurred()
    }
    
    func playPortfolioUpdateHaptic(isPositive: Bool) {
        guard !UIAccessibility.isReduceMotionEnabled else { return }
        notification.notificationOccurred(isPositive ? .success : .warning)
    }
    
    func playButtonTapHaptic() {
        guard !UIAccessibility.isReduceMotionEnabled else { return }
        impactMedium.impactOccurred()
    }
    
    func playErrorHaptic() {
        guard !UIAccessibility.isReduceMotionEnabled else { return }
        notification.notificationOccurred(.error)
    }
}

// MARK: - iOS 18 Features

@available(iOS 18.0, *)
struct iOS18FeaturesManager {
    static let shared = iOS18FeaturesManager()
    
    // MARK: - Control Center Widget
    func setupControlCenterWidget() {
        // Configure Control Center widget for quick portfolio access
        // Implementation would use iOS 18 Control Center APIs
    }
    
    // MARK: - Interactive Widgets
    func configureInteractiveWidgets() {
        // Setup interactive widgets with Apple Intelligence insights
        // Implementation would use iOS 18 Widget APIs
    }
    
    // MARK: - Enhanced Siri Integration
    func setupEnhancedSiri() {
        // Configure enhanced Siri with Apple Intelligence
        // Implementation would use iOS 18 Siri APIs
    }
}

// MARK: - Apple Design Award Ready Components

struct AppleDesignAwardButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    @StateObject private var accessibilityManager = AppleIntelligenceAccessibilityManager.shared
    @ObservedObject private var hapticManager = SmartHapticManager.shared
    
    var body: some View {
        Button(action: {
            hapticManager.playButtonTapHaptic()
            action()
        }) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.headline)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding()
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
            )
        }
        .accessibilityLabel("\(title) button")
        .accessibilityHint("Double tap to \(title.lowercased())")
        .scaleEffect(accessibilityManager.isDynamicTypeEnabled ? 1.2 : 1.0)
        .animation(
            accessibilityManager.isReduceMotionEnabled ? .none : .spring(response: 0.3),
            value: accessibilityManager.isDynamicTypeEnabled
        )
    }
}

struct AppleDesignAwardCard: View {
    let title: String
    let subtitle: String
    let content: AnyView
    
    @StateObject private var accessibilityManager = AppleIntelligenceAccessibilityManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(accessibilityManager.isDynamicTypeEnabled ? .title : .headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text(subtitle)
                    .font(accessibilityManager.isDynamicTypeEnabled ? .title3 : .subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            content
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.1),
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.3),
                                    Color.white.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title), \(subtitle)")
    }
}

// MARK: - Advanced Accessibility Features

struct VoiceOverOptimizedText: View {
    let text: String
    let isAIGenerated: Bool
    
    @StateObject private var accessibilityManager = AppleIntelligenceAccessibilityManager.shared
    
    var body: some View {
        Text(text)
            .font(dynamicFont)
            .foregroundColor(.primary)
            .accessibilityLabel(accessibilityLabel)
            .accessibilityHint(isAIGenerated ? "This content was generated by Apple Intelligence" : "")
    }
    
    private var dynamicFont: Font {
        switch accessibilityManager.preferredContentSizeCategory {
        case .extraSmall, .small, .medium:
            return .body
        case .large, .extraLarge:
            return .title3
        case .extraExtraLarge, .extraExtraExtraLarge:
            return .title2
        default:
            return .title
        }
    }
    
    private var accessibilityLabel: String {
        if isAIGenerated {
            return "Apple Intelligence says: \(text)"
        } else {
            return text
        }
    }
}

struct AccessibilityOptimizedChart: View {
    let data: [ChartDataPoint]
    let title: String
    
    @StateObject private var accessibilityManager = AppleIntelligenceAccessibilityManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .accessibilityAddTraits(.isHeader)
            
            if accessibilityManager.isVoiceOverEnabled {
                // Provide text-based data for VoiceOver users
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(Array(data.enumerated()), id: \.offset) { index, point in
                        Text("\(point.label ?? "Unknown"): \(String(format: "%.2f", point.y))")
                            .font(.caption)
                    }
                }
                .accessibilityElement(children: .combine)
                .accessibilityLabel("Chart data for \(title)")
            } else {
                // Visual chart for sighted users
                Rectangle()
                    .fill(Color.blue.opacity(0.3))
                    .frame(height: 200)
                    .overlay(
                        Text("Chart: \(title)")
                            .foregroundColor(.blue)
                    )
                    .accessibilityLabel("Chart showing \(title)")
                    .accessibilityValue(chartSummary)
            }
        }
    }
    
    private var chartSummary: String {
        let values = data.map { $0.y }
        let min = values.min() ?? 0
        let max = values.max() ?? 0
        let avg = values.reduce(0, +) / Double(values.count)
        
        return "Chart ranges from \(String(format: "%.2f", min)) to \(String(format: "%.2f", max)), with average of \(String(format: "%.2f", avg))"
    }
}

// MARK: - Extension for View Modifiers

extension View {
    func appleIntelligenceAccessible() -> some View {
        self.modifier(AppleIntelligenceAccessibilityModifier())
    }
    
    func voiceControlSupport(identifier: String, label: String) -> some View {
        self.modifier(VoiceControlSupport(identifier: identifier, label: label))
    }
    
    func smartHaptic(on trigger: some Equatable, style: UIImpactFeedbackGenerator.FeedbackStyle = .medium) -> some View {
        self.onChange(of: trigger) {
            guard !UIAccessibility.isReduceMotionEnabled else { return }
            UIImpactFeedbackGenerator(style: style).impactOccurred()
        }
    }
}

// MARK: - Supporting Data Models

// Using ChartDataPoint from AnalyticsModels.swift

extension ContentSizeCategory {
    init(_ uiContentSizeCategory: UIContentSizeCategory) {
        switch uiContentSizeCategory {
        case .extraSmall: self = .extraSmall
        case .small: self = .small
        case .medium: self = .medium
        case .large: self = .large
        case .extraLarge: self = .extraLarge
        case .extraExtraLarge: self = .extraExtraLarge
        case .extraExtraExtraLarge: self = .extraExtraExtraLarge
        case .accessibilityMedium: self = .accessibilityMedium
        case .accessibilityLarge: self = .accessibilityLarge
        case .accessibilityExtraLarge: self = .accessibilityExtraLarge
        case .accessibilityExtraExtraLarge: self = .accessibilityExtraExtraLarge
        case .accessibilityExtraExtraExtraLarge: self = .accessibilityExtraExtraExtraLarge
        default: self = .medium
        }
    }
}
