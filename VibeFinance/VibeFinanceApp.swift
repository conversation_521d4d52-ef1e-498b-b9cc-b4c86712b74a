//
//  VibeFinanceApp.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import SwiftData
import AppIntents

@main
struct VibeFinanceApp: App {
    @StateObject private var authManager = AuthManager()
    @StateObject private var userManager = UserManager()
    @StateObject private var feedManager = FeedManager()
    @StateObject private var questManager = QuestManager()
    @StateObject private var squadManager = SquadManager()
    @StateObject private var simulatorManager = SimulatorManager()
    @StateObject private var realTradingManager = RealTradingManager()
    @StateObject private var analyticsManager = AnalyticsManager()
    @StateObject private var chatManager = ChatManager()
    @StateObject private var subscriptionManager = SubscriptionManager()
    @StateObject private var paymentManager: PaymentManager
    @StateObject private var performanceManager = PerformanceManager.shared
    @StateObject private var networkOptimizer = NetworkOptimizer.shared
    @StateObject private var siriIntegrationManager = SiriIntegrationManager.shared
    @StateObject private var appleSiliconManager = AppleSiliconPerformanceManager.shared
    @StateObject private var batteryManager = BatteryAwarePerformanceManager.shared
    @StateObject private var memoryManager = MemoryOptimizationManager.shared

    init() {
        let subscriptionMgr = SubscriptionManager()
        _subscriptionManager = StateObject(wrappedValue: subscriptionMgr)
        _paymentManager = StateObject(wrappedValue: PaymentManager(subscriptionManager: subscriptionMgr))

        // Initialize production configuration
        setupProductionEnvironment()
    }

    private func setupProductionEnvironment() {
        // Log production readiness
        ProductionConfig.logProductionReadiness()

        // Configure logging level
        if ProductionConfig.isProduction {
            ProductionConfig.log("🚀 VibeFinance starting in PRODUCTION mode", category: "APP", level: .info)
        } else {
            ProductionConfig.log("🛠️ VibeFinance starting in DEVELOPMENT mode", category: "APP", level: .info)
        }

        // Log API configuration
        ProductionConfig.log("📡 Using real APIs: \(ProductionConfig.useRealAPIs)", category: "APP", level: .info)
        ProductionConfig.log("⚡ Real-time updates: \(ProductionConfig.enableRealTimeUpdates)", category: "APP", level: .info)

        // Start real-time services if enabled
        if ProductionConfig.enableRealTimeUpdates {
            MarketService.shared.startRealTimeUpdates()
        }
    }

    private func setupAppIntents() {
        // Configure App Intents and Siri integration
        ProductionConfig.log("🎤 Setting up App Intents and Siri integration", category: "INTENTS", level: .info)

        // Donate common shortcuts to Siri
        siriIntegrationManager.donateShortcut(
            for: CheckPortfolioIntent(),
            phrase: "Check my portfolio"
        )

        siriIntegrationManager.donateShortcut(
            for: GetAIAdviceIntent(),
            phrase: "Get investment advice"
        )

        siriIntegrationManager.donateShortcut(
            for: CheckMarketIntent(),
            phrase: "Check the market"
        )

        siriIntegrationManager.donateShortcut(
            for: ViewQuestsIntent(),
            phrase: "Show my learning progress"
        )

        ProductionConfig.log("✅ App Intents setup complete", category: "INTENTS", level: .info)
    }

    private func setupPerformanceOptimization() {
        // Initialize Apple Silicon optimizations
        ProductionConfig.log("🚀 Setting up Apple Silicon performance optimization", category: "PERFORMANCE", level: .info)

        // Configure Neural Engine for financial calculations
        appleSiliconManager.optimizeForFinancialCalculations()

        // Optimize chart rendering with Metal
        appleSiliconManager.optimizeForChartRendering()

        // Configure memory optimization for large datasets
        appleSiliconManager.optimizeMemoryForLargeDatasets()

        // Setup battery-aware performance monitoring
        batteryManager.trackBatteryUsage(for: "app_launch", duration: 2.0)

        // Initialize memory optimization
        memoryManager.startMemoryOptimization()

        ProductionConfig.log("✅ Performance optimization setup complete", category: "PERFORMANCE", level: .info)
    }

    var body: some Scene {
        WindowGroup {
            if authManager.isAuthenticated {
                MainTabView()
                    .environmentObject(authManager)
                    .environmentObject(userManager)
                    .environmentObject(feedManager)
                    .environmentObject(questManager)
                    .environmentObject(squadManager)
                    .environmentObject(simulatorManager)
                    .environmentObject(realTradingManager)
                    .environmentObject(analyticsManager)
                    .environmentObject(chatManager)
                    .environmentObject(subscriptionManager)
                    .environmentObject(paymentManager)
                    .environmentObject(performanceManager)
                    .environmentObject(networkOptimizer)
                    .environmentObject(siriIntegrationManager)
                    .environmentObject(appleSiliconManager)
                    .environmentObject(batteryManager)
                    .environmentObject(memoryManager)
                    .preferredColorScheme(.dark)
                    .onAppear {
                        setupAppIntents()
                        // setupPerformanceOptimization() // Temporarily disabled
                    }
            } else {
                OnboardingView()
                    .environmentObject(authManager)
                    .environmentObject(userManager)
                    .preferredColorScheme(.dark)
            }
        }
    }
}
